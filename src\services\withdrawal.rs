use std::collections::HashMap;
use std::sync::Arc;

use anyhow::{Context, Result, anyhow};
use chrono::Utc;
use log::{error, info, warn};
use mongodb::bson::{doc, oid::ObjectId, Document};
use serde_json::json;
use tokio::sync::RwLock;

use crate::database::Database;
use crate::models::payout::{Payout, PayoutStatus, PayoutRequest, PayoutResponse, PayoutAccount, Currency};
use crate::models::webhook::{WebhookEvent, WebhookCallbackResponse};
use crate::services::stripe::StripeService;
use crate::utils::validation::validate_amount;

/// Withdrawal service
pub struct WithdrawalService {
    db: Arc<Database>,
    stripe_service: Arc<StripeService>,
    webhook_cache: Arc<RwLock<HashMap<String, bool>>>,
}

impl WithdrawalService {
    /// Create new withdrawal service
    pub fn new(db: Arc<Database>, stripe_service: Arc<StripeService>) -> Self {
        Self {
            db,
            stripe_service,
            webhook_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Create a payout
    pub async fn create_payout(
        &self,
        user_id: ObjectId,
        request: PayoutRequest,
    ) -> Result<PayoutResponse> {
        // Validate amount
        if !validate_amount(request.amount) {
            return Ok(PayoutResponse::error(
                "INVALID_AMOUNT",
                Some(json!({"min": 1.0, "max": 1000000.0}))
            ));
        }

        // Get payout account
        let payout_account = self.get_payout_account(&user_id, &request.destination).await?;
        if payout_account.is_none() {
            return Ok(PayoutResponse::error(
                "PAYOUT_ACCOUNT_NOT_FOUND",
                None
            ));
        }

        let account = payout_account.unwrap();

        // Validate currency
        let currency = match request.currency.to_uppercase().as_str() {
            "USD" => Currency::USD,
            _ => {
                return Ok(PayoutResponse::error(
                    "UNSUPPORTED_CURRENCY",
                    Some(json!({"supported": ["USD"]}))
                ));
            }
        };

        // Calculate fees
        let fee_breakdown = self.calculate_fees(request.amount, &currency).await?;

        // Create payout record
        let mut payout = Payout {
            id: None,
            user_id,
            amount: request.amount,
            currency,
            destination: account.into(),
            status: PayoutStatus::Pending,
            processor: "stripe".to_string(),
            processor_response: None,
            fee_breakdown,
            net_amount: request.amount - fee_breakdown.total_fee,
            note: request.note,
            reference: self.generate_reference().await,
            metadata: request.metadata,
            created_at: Some(Utc::now()),
            updated_at: Some(Utc::now()),
            processed_at: None,
            completed_at: None,
        };

        // Save to database
        let collection = self.db.collection::<Payout>("payouts");
        let result = collection.insert_one(&payout, None).await?;
        payout.id = Some(result.inserted_id.as_object_id().unwrap());

        // Process with Stripe
        match self.process_stripe_payout(&payout).await {
            Ok(stripe_response) => {
                // Update payout with Stripe response
                payout.processor_response = Some(stripe_response);
                payout.status = PayoutStatus::Processing;
                payout.processed_at = Some(Utc::now());

                // Update in database
                self.update_payout_status(&payout.id.unwrap(), PayoutStatus::Processing).await?;

                info!("Payout created successfully: {}", payout.reference);
                Ok(PayoutResponse::success(payout))
            }
            Err(e) => {
                // Update status to failed
                payout.status = PayoutStatus::Failed;
                self.update_payout_status(&payout.id.unwrap(), PayoutStatus::Failed).await?;

                error!("Failed to process payout with Stripe: {}", e);
                Ok(PayoutResponse::error(
                    "PAYOUT_PROCESSING_FAILED",
                    Some(json!({"error": e.to_string()}))
                ))
            }
        }
    }

    /// Process Stripe webhook
    pub async fn process_stripe_webhook(
        &self,
        event_type: &str,
        data: serde_json::Value,
    ) -> Result<WebhookCallbackResponse> {
        // Check for duplicate webhook
        let event_id = data.get("id")
            .and_then(|id| id.as_str())
            .unwrap_or("unknown");

        {
            let cache = self.webhook_cache.read().await;
            if cache.contains_key(event_id) {
                return Ok(WebhookCallbackResponse::success());
            }
        }

        // Mark as processed
        {
            let mut cache = self.webhook_cache.write().await;
            cache.insert(event_id.to_string(), true);
        }

        match event_type {
            "payout.paid" => {
                self.handle_payout_paid(data).await
            }
            "payout.failed" => {
                self.handle_payout_failed(data).await
            }
            "payout.canceled" => {
                self.handle_payout_canceled(data).await
            }
            "transfer.created" => {
                self.handle_transfer_created(data).await
            }
            "transfer.updated" => {
                self.handle_transfer_updated(data).await
            }
            _ => {
                info!("Unhandled webhook event type: {}", event_type);
                Ok(WebhookCallbackResponse::success())
            }
        }
    }

    /// Handle payout paid webhook
    async fn handle_payout_paid(&self, data: serde_json::Value) -> Result<WebhookCallbackResponse> {
        let payout_id = data.get("id")
            .and_then(|id| id.as_str())
            .context("Missing payout ID")?;

        // Find payout by Stripe ID
        let collection = self.db.collection::<Payout>("payouts");
        let filter = doc! {
            "processor_response.id": payout_id
        };

        if let Some(mut payout) = collection.find_one(filter, None).await? {
            payout.status = PayoutStatus::Completed;
            payout.completed_at = Some(Utc::now());
            payout.updated_at = Some(Utc::now());

            // Update in database
            let update_doc = doc! {
                "$set": {
                    "status": "completed",
                    "completed_at": payout.completed_at,
                    "updated_at": payout.updated_at
                }
            };

            collection.update_one(
                doc! { "_id": payout.id.unwrap() },
                update_doc,
                None
            ).await?;

            // Trigger webhook for client
            self.trigger_client_webhook("payout.completed", &payout).await?;

            info!("Payout completed: {}", payout.reference);
        }

        Ok(WebhookCallbackResponse::success())
    }

    /// Handle payout failed webhook
    async fn handle_payout_failed(&self, data: serde_json::Value) -> Result<WebhookCallbackResponse> {
        let payout_id = data.get("id")
            .and_then(|id| id.as_str())
            .context("Missing payout ID")?;

        let failure_reason = data.get("failure_message")
            .and_then(|msg| msg.as_str())
            .unwrap_or("Unknown error");

        // Find payout by Stripe ID
        let collection = self.db.collection::<Payout>("payouts");
        let filter = doc! {
            "processor_response.id": payout_id
        };

        if let Some(mut payout) = collection.find_one(filter, None).await? {
            payout.status = PayoutStatus::Failed;
            payout.updated_at = Some(Utc::now());

            // Update processor response with failure details
            if let Some(ref mut response) = payout.processor_response {
                response.status = "failed".to_string();
                response.metadata = Some(doc! {
                    "failure_reason": failure_reason
                });
            }

            // Update in database
            let update_doc = doc! {
                "$set": {
                    "status": "failed",
                    "processor_response": mongodb::bson::to_bson(&payout.processor_response)?,
                    "updated_at": payout.updated_at
                }
            };

            collection.update_one(
                doc! { "_id": payout.id.unwrap() },
                update_doc,
                None
            ).await?;

            // Trigger webhook for client
            self.trigger_client_webhook("payout.failed", &payout).await?;

            warn!("Payout failed: {} - Reason: {}", payout.reference, failure_reason);
        }

        Ok(WebhookCallbackResponse::success())
    }

    /// Handle payout canceled webhook
    async fn handle_payout_canceled(&self, data: serde_json::Value) -> Result<WebhookCallbackResponse> {
        let payout_id = data.get("id")
            .and_then(|id| id.as_str())
            .context("Missing payout ID")?;

        // Find payout by Stripe ID
        let collection = self.db.collection::<Payout>("payouts");
        let filter = doc! {
            "processor_response.id": payout_id
        };

        if let Some(mut payout) = collection.find_one(filter, None).await? {
            payout.status = PayoutStatus::Cancelled;
            payout.updated_at = Some(Utc::now());

            // Update in database
            let update_doc = doc! {
                "$set": {
                    "status": "cancelled",
                    "updated_at": payout.updated_at
                }
            };

            collection.update_one(
                doc! { "_id": payout.id.unwrap() },
                update_doc,
                None
            ).await?;

            // Trigger webhook for client
            self.trigger_client_webhook("payout.cancelled", &payout).await?;

            info!("Payout cancelled: {}", payout.reference);
        }

        Ok(WebhookCallbackResponse::success())
    }

    /// Handle transfer created webhook
    async fn handle_transfer_created(&self, data: serde_json::Value) -> Result<WebhookCallbackResponse> {
        info!("Transfer created: {:?}", data.get("id"));
        // Additional transfer handling logic can be added here
        Ok(WebhookCallbackResponse::success())
    }

    /// Handle transfer updated webhook
    async fn handle_transfer_updated(&self, data: serde_json::Value) -> Result<WebhookCallbackResponse> {
        info!("Transfer updated: {:?}", data.get("id"));
        // Additional transfer handling logic can be added here
        Ok(WebhookCallbackResponse::success())
    }

    /// Get payout account by ID
    async fn get_payout_account(&self, user_id: &ObjectId, account_id: &str) -> Result<Option<PayoutAccount>> {
        let collection = self.db.collection::<PayoutAccount>("payout_accounts");
        let account_object_id = ObjectId::parse_str(account_id)?;

        let filter = doc! {
            "_id": account_object_id,
            "user_id": user_id
        };

        Ok(collection.find_one(filter, None).await?)
    }

    /// Calculate fees
    async fn calculate_fees(&self, amount: f64, currency: &Currency) -> Result<crate::models::payout::FeeBreakdown> {
        let (base_fee, percentage) = match currency {
            Currency::USD => (2.50, 0.025), // $2.50 + 2.5%
        };

        let percentage_fee = amount * percentage;
        let total_fee = base_fee + percentage_fee;
        let net_amount = amount - total_fee;

        Ok(crate::models::payout::FeeBreakdown {
            base_fee,
            percentage_fee,
            total_fee,
            net_amount,
        })
    }

    /// Generate unique reference
    async fn generate_reference(&self) -> String {
        format!("WD_{}", Utc::now().timestamp_millis())
    }

    /// Update payout status
    async fn update_payout_status(&self, payout_id: &ObjectId, status: PayoutStatus) -> Result<()> {
        let collection = self.db.collection::<Payout>("payouts");
        let update_doc = doc! {
            "$set": {
                "status": mongodb::bson::to_bson(&status)?,
                "updated_at": Utc::now()
            }
        };

        collection.update_one(
            doc! { "_id": payout_id },
            update_doc,
            None
        ).await?;

        Ok(())
    }

    /// Process payout with Stripe
    async fn process_stripe_payout(&self, payout: &Payout) -> Result<crate::models::payout::ProcessorResponse> {
        // Convert amount to cents for Stripe
        let amount_cents = (payout.amount * 100.0) as i64;
        let currency_str = match payout.currency {
            Currency::USD => "usd",
        };

        // Create Stripe payout
        let stripe_payout = self.stripe_service.create_payout(
            amount_cents,
            currency_str,
            &format!("Payout for {}", payout.reference)
        ).await?;

        // Convert Stripe response to our format
        Ok(crate::models::payout::ProcessorResponse {
            provider: "stripe".to_string(),
            id: stripe_payout.get("id").and_then(|v| v.as_str()).unwrap_or("").to_string(),
            status: stripe_payout.get("status").and_then(|v| v.as_str()).unwrap_or("pending").to_string(),
            amount: payout.amount,
            currency: currency_str.to_string(),
            created: Utc::now(),
            arrival_date: None, // Will be updated from webhook
            metadata: Some(doc! {
                "reference": &payout.reference,
                "user_id": payout.user_id.to_hex()
            }),
        })
    }

    /// Trigger client webhook
    async fn trigger_client_webhook(&self, event_type: &str, payout: &Payout) -> Result<()> {
        let webhook_event = WebhookEvent {
            event: event_type.to_string(),
            data: serde_json::to_value(payout)?,
            timestamp: Utc::now().to_rfc3339(),
            client_id: payout.user_id.to_hex(),
            metadata: payout.metadata.clone(),
        };

        // Here you would typically send the webhook to registered client endpoints
        // For now, we'll just log it
        info!("Webhook triggered: {} for payout {}", event_type, payout.reference);

        Ok(())
    }
}
