use actix_web::{App, HttpServer, web};
use dotenv::dotenv;
use log::{info, error};
use std::env;
use std::sync::Arc;
use tokio::time::{interval, Duration};

mod config;
mod controllers;
mod database;
mod middleware;
mod models;
mod routes;
mod services;
mod utils;

use database::DatabaseService;
use services::cache::CacheService;
use services::token::TokenService;
use services::webhook::WebhookService;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Load environment variables
    dotenv().ok();

    // Initialize logger
    env_logger::init_from_env(env_logger::Env::default().default_filter_or("info"));

    info!("Starting SangaPay Transfer Service - Critical Endpoints Only");

    // Get server configuration
    let host = env::var("HOST").unwrap_or_else(|_| "127.0.0.1".to_string());
    let port = env::var("PORT").unwrap_or_else(|_| "5000".to_string()).parse::<u16>().unwrap();

    // Initialize database connection
    let database_service = match DatabaseService::new().await {
        Ok(service) => {
            info!("Database connection established");
            Arc::new(service)
        },
        Err(e) => {
            error!("Failed to connect to database: {}", e);
            return Err(std::io::Error::new(std::io::ErrorKind::Other, "Database connection failed"));
        }
    };

    // Initialize cache service
    let cache_service = match CacheService::new().await {
        Ok(service) => {
            info!("Cache service initialized");
            Arc::new(service)
        },
        Err(e) => {
            error!("Failed to initialize cache service: {}", e);
            info!("Continuing without cache service");
            Arc::new(CacheService::new_memory_only())
        }
    };


    // Initialize token service
    let token_service = match TokenService::new() {
        Ok(service) => {
            info!("Token service initialized");
            Some(Arc::new(service))
        },
        Err(e) => {
            error!("Failed to initialize token service: {}", e);
            None
        }
    };

    // Initialize webhook service
    let webhook_service = Arc::new(WebhookService::new());
    info!("Webhook service initialized");

    // Start background webhook retry service
    let webhook_service_clone = webhook_service.clone();
    let database_service_clone = database_service.clone();
    tokio::spawn(async move {
        let mut interval = interval(Duration::from_secs(60)); // Every minute
        loop {
            interval.tick().await;
            if let Err(e) = webhook_service_clone.process_failed_webhooks(database_service_clone.database()).await {
                error!("Error processing failed webhooks: {}", e);
            }
        }
    });

    // Start webhook cleanup service
    let webhook_service_cleanup = webhook_service.clone();
    let database_service_cleanup = database_service.clone();
    tokio::spawn(async move {
        let mut interval = interval(Duration::from_secs(3600)); // Every hour
        loop {
            interval.tick().await;
            if let Err(e) = webhook_service_cleanup.cleanup_old_data(database_service_cleanup.database()).await {
                error!("Error cleaning up webhook data: {}", e);
            }
        }
    });

    // Create application state
    let app_state = web::Data::new(AppState {
        db: database_service.clone(),
        cache: cache_service.clone(),
        token_service,
        webhook_service: webhook_service.clone(),
    });

    // Start HTTP server
    info!("Starting HTTP server on {}:{}", host, port);
    HttpServer::new(move || {
        App::new()
            // Add application state
            .app_data(app_state.clone())

            // Security middleware is temporarily disabled due to compatibility issues
            // .wrap(crate::middleware::security::configure_security())

            // Apply logger middleware
            .wrap(actix_web::middleware::Logger::default())

            // Apply CORS middleware
            .wrap(actix_cors::Cors::default()
                .send_wildcard() // Use wildcard for CORS
                .allowed_methods(vec!["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])
                .allowed_headers(vec![
                    actix_web::http::header::CONTENT_TYPE,
                    actix_web::http::header::AUTHORIZATION,
                    actix_web::http::header::ACCEPT
                ])
                .max_age(86400)) // 24 hours

            // Configure routes
            .configure(routes::configure_routes)

            // Default handler for 404 errors
            .default_service(web::route().to(routes::not_found))
    })
    .bind((host, port))?
    .run()
    .await
}

// Application state shared across all routes
pub struct AppState {
    pub db: Arc<DatabaseService>,
    pub cache: Arc<CacheService>,
    pub token_service: Option<Arc<TokenService>>,
    pub webhook_service: Arc<WebhookService>,
}


