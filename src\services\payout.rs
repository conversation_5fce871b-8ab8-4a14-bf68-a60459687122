use std::collections::HashMap;
use std::sync::Arc;

use anyhow::{Context, Result, anyhow};
use backoff::{ExponentialBackoff, future::retry};
use chrono::Utc;
use log::{error, info, warn};
use mongodb::{Database, bson::{doc, oid::ObjectId}};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::models::{
    account::Account,
    transaction::{SimpleTransaction as Transaction, TransactionStatus, TransactionType},
};
use crate::services::{
    account_number::AccountNumberService,
    stripe::StripeService,
};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum PayoutStatus {
    Pending,
    Processing,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum PayoutMethod {
    BankTransfer,
    Stripe,
    Crypto,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Payout {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub payout_id: String,
    pub user_id: ObjectId,
    pub account_id: ObjectId,
    pub amount: f64,
    pub currency: String,
    pub method: PayoutMethod,
    pub status: PayoutStatus,
    pub destination: PayoutDestination,
    pub metadata: Option<serde_json::Value>,
    pub transaction_id: Option<String>,
    pub external_reference: Option<String>,
    pub fee: f64,
    pub net_amount: f64,
    pub description: String,
    pub created_at: chrono::DateTime<Utc>,
    pub updated_at: chrono::DateTime<Utc>,
    pub processed_at: Option<chrono::DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PayoutDestination {
    pub account_number: Option<String>,
    pub bank_code: Option<String>,
    pub bank_name: Option<String>,
    pub account_holder_name: String,
    pub phone_number: Option<String>,
    pub email: Option<String>,
    pub address: Option<PayoutAddress>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PayoutAddress {
    pub street: String,
    pub city: String,
    pub state: String,
    pub country: String,
    pub postal_code: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PayoutRequest {
    pub amount: f64,
    pub currency: String,
    pub method: PayoutMethod,
    pub destination: PayoutDestination,
    pub description: Option<String>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PayoutResponse {
    pub success: bool,
    pub payout_id: String,
    pub status: PayoutStatus,
    pub message: String,
    pub estimated_completion: Option<chrono::DateTime<Utc>>,
}

pub struct PayoutService {
    db: Arc<Database>,
    stripe_service: StripeService,
    account_service: AccountNumberService,
}

impl PayoutService {
    pub fn new(db: Arc<Database>) -> Result<Self> {
        let stripe_service = StripeService::new()?;
        let account_service = AccountNumberService::new(db.clone());

        Ok(Self {
            db,
            stripe_service,
            account_service,
        })
    }

    pub async fn create_payout(
        &self,
        user_id: ObjectId,
        account_id: ObjectId,
        request: PayoutRequest,
    ) -> Result<PayoutResponse> {
        // Validate the request
        self.validate_payout_request(&request)?;

        // Get user account
        let mut account = self.get_account(account_id).await?;

        // Check if user has sufficient balance
        let currency_balance = account.balance.iter_mut()
            .find(|b| b.currency == request.currency)
            .ok_or_else(|| anyhow!("Currency {} not supported", request.currency))?;

        if currency_balance.walletBalance < request.amount {
            return Ok(PayoutResponse {
                success: false,
                payout_id: String::new(),
                status: PayoutStatus::Failed,
                message: "Insufficient balance".to_string(),
                estimated_completion: None,
            });
        }

        // Calculate fee
        let fee = self.calculate_payout_fee(request.amount, &request.method, &request.currency);
        let net_amount = request.amount - fee;

        if net_amount <= 0.0 {
            return Ok(PayoutResponse {
                success: false,
                payout_id: String::new(),
                status: PayoutStatus::Failed,
                message: "Amount too small after fees".to_string(),
                estimated_completion: None,
            });
        }

        // Create payout record
        let payout_id = format!("payout_{}", Uuid::new_v4().to_string().replace("-", ""));
        let now = Utc::now();

        let payout = Payout {
            id: None,
            payout_id: payout_id.clone(),
            user_id,
            account_id,
            amount: request.amount,
            currency: request.currency.clone(),
            method: request.method.clone(),
            status: PayoutStatus::Pending,
            destination: request.destination,
            metadata: request.metadata,
            transaction_id: None,
            external_reference: None,
            fee,
            net_amount,
            description: request.description.unwrap_or_else(|| "Payout".to_string()),
            created_at: now,
            updated_at: now,
            processed_at: None,
        };

        // Save payout to database
        let save_payout = || async {
            self.db
                .collection::<Payout>("payouts")
                .insert_one(&payout, None)
                .await
        };

        let insert_result = retry(ExponentialBackoff::default(), save_payout).await?;
        let payout_object_id = insert_result.inserted_id.as_object_id().unwrap();

        // Reserve the amount (deduct from wallet balance)
        currency_balance.walletBalance -= request.amount;

        // Update account
        let update_account = || async {
            self.db
                .collection::<Account>("accounts")
                .replace_one(
                    doc! { "_id": account_id },
                    &account,
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_account).await?;

        // Create transaction record
        let transaction_id = self.account_service.generate_transaction_id(
            &format!("payout_{}_{}", payout_id, now.timestamp())
        );

        let transaction = Transaction {
            id: None,
            user_id,
            account_id,
            transaction_id: transaction_id.clone(),
            transaction_type: TransactionType::Payout.to_string(),
            amount: request.amount,
            currency: request.currency.clone(),
            status: TransactionStatus::Pending.to_string(),
            description: format!("Payout via {:?}", request.method),
            metadata: Some(serde_json::json!({
                "payout_id": payout_id,
                "method": request.method,
                "fee": fee,
                "net_amount": net_amount
            })),
            created_at: now,
            updated_at: now,
        };

        let save_transaction = || async {
            self.db
                .collection::<Transaction>("transactions")
                .insert_one(&transaction, None)
                .await
        };

        retry(ExponentialBackoff::default(), save_transaction).await?;

        // Update payout with transaction ID
        let update_payout = || async {
            self.db
                .collection::<Payout>("payouts")
                .update_one(
                    doc! { "_id": payout_object_id },
                    doc! { "$set": { "transactionId": &transaction_id } },
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_payout).await?;

        // Process the payout asynchronously
        let db_clone = self.db.clone();
        let payout_id_clone = payout_id.clone();
        let method_clone = request.method.clone();
        
        tokio::spawn(async move {
            let payout_service = PayoutService::new(db_clone).unwrap();
            if let Err(e) = payout_service.process_payout(&payout_id_clone, method_clone).await {
                error!("Failed to process payout {}: {}", payout_id_clone, e);
            }
        });

        info!("Payout created: {}", payout_id);

        Ok(PayoutResponse {
            success: true,
            payout_id,
            status: PayoutStatus::Pending,
            message: "Payout created successfully".to_string(),
            estimated_completion: Some(now + chrono::Duration::hours(24)),
        })
    }

    async fn process_payout(&self, payout_id: &str, method: PayoutMethod) -> Result<()> {
        // Update status to processing
        self.update_payout_status(payout_id, PayoutStatus::Processing).await?;

        let result = match method {
            PayoutMethod::Stripe => self.process_stripe_payout(payout_id).await,
            PayoutMethod::BankTransfer => self.process_bank_transfer_payout(payout_id).await,
            PayoutMethod::Crypto => self.process_crypto_payout(payout_id).await,
        };

        match result {
            Ok(external_ref) => {
                self.complete_payout(payout_id, external_ref).await?;
                info!("Payout completed: {}", payout_id);
            }
            Err(e) => {
                self.fail_payout(payout_id, &e.to_string()).await?;
                error!("Payout failed: {} - {}", payout_id, e);
            }
        }

        Ok(())
    }

    async fn process_stripe_payout(&self, payout_id: &str) -> Result<String> {
        // Get payout details
        let payout = self.get_payout_by_id(payout_id).await?;
        
        // Create Stripe payout
        let stripe_payout = self.stripe_service.create_payout(
            (payout.net_amount * 100.0) as i64, // Convert to cents
            &payout.currency.to_lowercase(),
            &format!("Payout {}", payout_id),
        ).await?;

        Ok(stripe_payout["id"].as_str().unwrap_or("unknown").to_string())
    }

    async fn process_bank_transfer_payout(&self, _payout_id: &str) -> Result<String> {
        // Simulate bank transfer processing
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
        Ok(format!("bank_ref_{}", Uuid::new_v4().to_string().replace("-", "")))
    }



    async fn process_crypto_payout(&self, _payout_id: &str) -> Result<String> {
        // Simulate crypto processing
        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
        Ok(format!("crypto_ref_{}", Uuid::new_v4().to_string().replace("-", "")))
    }

    async fn complete_payout(&self, payout_id: &str, external_reference: String) -> Result<()> {
        let now = Utc::now();
        
        let update_payout = || async {
            self.db
                .collection::<Payout>("payouts")
                .update_one(
                    doc! { "payoutId": payout_id },
                    doc! { 
                        "$set": { 
                            "status": bson::to_bson(&PayoutStatus::Completed).unwrap(),
                            "externalReference": &external_reference,
                            "processedAt": now,
                            "updatedAt": now
                        }
                    },
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_payout).await?;

        // Update transaction status
        let update_transaction = || async {
            self.db
                .collection::<Transaction>("transactions")
                .update_one(
                    doc! { "metadata.payout_id": payout_id },
                    doc! { 
                        "$set": { 
                            "status": TransactionStatus::Completed.to_string(),
                            "updatedAt": now
                        }
                    },
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_transaction).await?;

        Ok(())
    }

    async fn fail_payout(&self, payout_id: &str, error_message: &str) -> Result<()> {
        let now = Utc::now();
        
        // Get payout to refund the amount
        let payout = self.get_payout_by_id(payout_id).await?;
        
        // Refund the amount to user's wallet
        let mut account = self.get_account(payout.account_id).await?;
        if let Some(balance) = account.balance.iter_mut().find(|b| b.currency == payout.currency) {
            balance.walletBalance += payout.amount;
        }

        // Update account
        let update_account = || async {
            self.db
                .collection::<Account>("accounts")
                .replace_one(
                    doc! { "_id": payout.account_id },
                    &account,
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_account).await?;

        // Update payout status
        let update_payout = || async {
            self.db
                .collection::<Payout>("payouts")
                .update_one(
                    doc! { "payoutId": payout_id },
                    doc! { 
                        "$set": { 
                            "status": bson::to_bson(&PayoutStatus::Failed).unwrap(),
                            "metadata.error": error_message,
                            "updatedAt": now
                        }
                    },
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_payout).await?;

        // Update transaction status
        let update_transaction = || async {
            self.db
                .collection::<Transaction>("transactions")
                .update_one(
                    doc! { "metadata.payout_id": payout_id },
                    doc! { 
                        "$set": { 
                            "status": TransactionStatus::Failed.to_string(),
                            "updatedAt": now
                        }
                    },
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_transaction).await?;

        Ok(())
    }

    async fn update_payout_status(&self, payout_id: &str, status: PayoutStatus) -> Result<()> {
        let update_status = || async {
            self.db
                .collection::<Payout>("payouts")
                .update_one(
                    doc! { "payoutId": payout_id },
                    doc! { 
                        "$set": { 
                            "status": bson::to_bson(&status).unwrap(),
                            "updatedAt": Utc::now()
                        }
                    },
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_status).await?;
        Ok(())
    }

    async fn get_payout_by_id(&self, payout_id: &str) -> Result<Payout> {
        let find_payout = || async {
            self.db
                .collection::<Payout>("payouts")
                .find_one(doc! { "payoutId": payout_id }, None)
                .await
        };

        match retry(ExponentialBackoff::default(), find_payout).await? {
            Some(payout) => Ok(payout),
            None => Err(anyhow!("Payout not found: {}", payout_id)),
        }
    }

    async fn get_account(&self, account_id: ObjectId) -> Result<Account> {
        let find_account = || async {
            self.db
                .collection::<Account>("accounts")
                .find_one(doc! { "_id": account_id }, None)
                .await
        };

        match retry(ExponentialBackoff::default(), find_account).await? {
            Some(account) => Ok(account),
            None => Err(anyhow!("Account not found")),
        }
    }

    fn validate_payout_request(&self, request: &PayoutRequest) -> Result<()> {
        if request.amount <= 0.0 {
            return Err(anyhow!("Amount must be greater than zero"));
        }

        if request.currency.is_empty() {
            return Err(anyhow!("Currency is required"));
        }

        if request.destination.account_holder_name.is_empty() {
            return Err(anyhow!("Account holder name is required"));
        }

        match request.method {
            PayoutMethod::BankTransfer => {
                if request.destination.account_number.is_none() || request.destination.bank_code.is_none() {
                    return Err(anyhow!("Bank account details are required for bank transfer"));
                }
            }

            _ => {}
        }

        Ok(())
    }

    fn calculate_payout_fee(&self, amount: f64, method: &PayoutMethod, _currency: &str) -> f64 {
        match method {
            PayoutMethod::BankTransfer => amount * 0.01, // 1%
            PayoutMethod::Stripe => amount * 0.015, // 1.5%
            PayoutMethod::Crypto => amount * 0.002, // 0.2%
        }
    }

    pub async fn get_payout_status(&self, payout_id: &str) -> Result<PayoutStatus> {
        let payout = self.get_payout_by_id(payout_id).await?;
        Ok(payout.status)
    }

    pub async fn cancel_payout(&self, payout_id: &str) -> Result<()> {
        let payout = self.get_payout_by_id(payout_id).await?;
        
        if !matches!(payout.status, PayoutStatus::Pending) {
            return Err(anyhow!("Can only cancel pending payouts"));
        }

        // Refund the amount
        let mut account = self.get_account(payout.account_id).await?;
        if let Some(balance) = account.balance.iter_mut().find(|b| b.currency == payout.currency) {
            balance.walletBalance += payout.amount;
        }

        // Update account
        let update_account = || async {
            self.db
                .collection::<Account>("accounts")
                .replace_one(
                    doc! { "_id": payout.account_id },
                    &account,
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_account).await?;

        // Update payout status
        self.update_payout_status(payout_id, PayoutStatus::Cancelled).await?;

        info!("Payout cancelled: {}", payout_id);
        Ok(())
    }
}
