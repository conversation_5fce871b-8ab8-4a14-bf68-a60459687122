use serde::{Deserialize, Serialize};
use std::env;
use log::info;

/// Database configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// MongoDB URI
    pub uri: String,
    /// Maximum pool size
    pub max_pool_size: u32,
    /// Minimum pool size
    pub min_pool_size: u32,
    /// Connection timeout in milliseconds
    pub connect_timeout_ms: u64,
    /// Socket timeout in milliseconds
    pub socket_timeout_ms: u64,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            uri: env::var("MONGODB_URI").unwrap_or_else(|_| "mongodb://localhost:27017/sangapay".to_string()),
            max_pool_size: env::var("MONGODB_POOL_SIZE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(100),
            min_pool_size: 20,
            connect_timeout_ms: 30000,
            socket_timeout_ms: 45000,
        }
    }
}

/// Redis cache configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CacheConfig {
    /// Redis URL
    pub url: Option<String>,
    /// Default TTL in seconds
    pub default_ttl: u64,
    /// Whether to use memory fallback when Redis is unavailable
    pub use_memory_fallback: bool,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            url: env::var("REDIS_URL").ok(),
            default_ttl: 3600,
            use_memory_fallback: true,
        }
    }
}

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// Host to bind to
    pub host: String,
    /// Port to listen on
    pub port: u16,
    /// Whether to enable clustering
    pub enable_clustering: bool,
    /// Number of workers
    pub workers: usize,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: env::var("HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
            port: env::var("PORT")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(5000),
            enable_clustering: env::var("ENABLE_CLUSTERING")
                .map(|v| v == "true")
                .unwrap_or(false),
            workers: env::var("WORKERS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or_else(|| num_cpus::get()),
        }
    }
}

/// Security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// JWT secret
    pub jwt_secret: String,
    /// JWT expiration time in seconds
    pub jwt_expiration: u64,
    /// Rate limit window in milliseconds
    pub rate_limit_window_ms: u64,
    /// Maximum requests per window
    pub rate_limit_max_requests: u32,
    /// CORS allowed origins
    pub cors_origin: String,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            jwt_secret: env::var("JWT_SECRET").unwrap_or_else(|_| "sangapay_secret_key".to_string()),
            jwt_expiration: 3600,
            rate_limit_window_ms: env::var("RATE_LIMIT_WINDOW_MS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(900000), // 15 minutes
            rate_limit_max_requests: env::var("RATE_LIMIT_MAX_REQUESTS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(100),
            cors_origin: env::var("CORS_ORIGIN").unwrap_or_else(|_| "*".to_string()),
        }
    }
}

/// Payment processor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentProcessorConfig {
    /// Name of the processor
    pub name: String,
    /// Display name
    pub display_name: String,
    /// Description
    pub description: String,
    /// Whether the processor is enabled
    pub enabled: bool,
    /// Minimum amount
    pub min_amount: f64,
    /// Maximum amount
    pub max_amount: f64,
    /// Fee percentage
    pub fee: f64,
    /// Processing time description
    pub processing_time: String,
    /// Required fields
    pub required_fields: Vec<String>,
}

/// Application configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// Database configuration
    pub database: DatabaseConfig,
    /// Cache configuration
    pub cache: CacheConfig,
    /// Server configuration
    pub server: ServerConfig,
    /// Security configuration
    pub security: SecurityConfig,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig::default(),
            cache: CacheConfig::default(),
            server: ServerConfig::default(),
            security: SecurityConfig::default(),
        }
    }
}

/// Load configuration from environment variables
pub fn load_config() -> AppConfig {
    let config = AppConfig::default();
    info!("Configuration loaded");
    config
}

/// Get payment processor configuration for a currency
pub fn get_payment_processor(currency: &str) -> Option<PaymentProcessorConfig> {
    match currency {
        "USD" => Some(PaymentProcessorConfig {
            name: "stripe".to_string(),
            display_name: "Stripe".to_string(),
            description: "Stripe payment processor for USD withdrawals".to_string(),
            enabled: true,
            min_amount: 10.0,
            max_amount: 10000.0,
            fee: 0.0,
            processing_time: "1-3 business days".to_string(),
            required_fields: vec![
                "accountNumber".to_string(),
                "accountName".to_string(),
                "bankName".to_string(),
                "bankCode".to_string(),
            ],
        }),

        _ => None,
    }
}
