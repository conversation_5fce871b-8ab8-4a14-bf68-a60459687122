use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpMessage,
};
use actix_cors::Cors;
use actix_web::http::header;
use actix_web::http::header::{HeaderName, HeaderValue};
use futures_util::future::{ready, LocalBoxFuture, Ready};
use log::{info, warn, error};
use std::future::Future;
use std::pin::Pin;
use std::task::{Context, Poll};
use std::env;
use std::rc::Rc;
use std::time::Duration;
use actix_web::{HttpRequest, web};
use governor::{Quota, RateLimiter};
use governor::clock::{DefaultClock, Clock};
use governor::middleware::NoOpMiddleware;
use governor::state::{InMemoryState, NotKeyed};
use std::num::NonZeroU32;

/// Security middleware for the application
pub struct SecurityMiddleware {
    rate_limiter: Rc<RateLimiter<NotKeyed, InMemoryState, DefaultClock, NoOpMiddleware>>,
}

impl SecurityMiddleware {
    /// Create a new security middleware
    pub fn new() -> Self {
        // Configure rate limiter
        let rate_limit_per_second = env::var("RATE_LIMIT_PER_SECOND")
            .unwrap_or_else(|_| "10".to_string())
            .parse::<u32>()
            .unwrap_or(10);

        let quota = Quota::per_second(NonZeroU32::new(rate_limit_per_second).unwrap());
        let rate_limiter = RateLimiter::direct(quota);

        Self {
            rate_limiter: Rc::new(rate_limiter),
        }
    }
}

impl<S, B> Transform<S, ServiceRequest> for SecurityMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + Clone + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = SecurityMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(SecurityMiddlewareService {
            service,
            rate_limiter: self.rate_limiter.clone(),
        }))
    }
}

pub struct SecurityMiddlewareService<S> {
    service: S,
    rate_limiter: Rc<RateLimiter<NotKeyed, InMemoryState, DefaultClock, NoOpMiddleware>>,
}

impl<S, B> Service<ServiceRequest> for SecurityMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + Clone + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        // Skip rate limiting for health check endpoints
        let path = req.path();
        let skip_rate_limiting = path == "/api/health" || path == "/api/v1/health";

        // Apply rate limiting
        let rate_limit_result = if skip_rate_limiting {
            Ok(())
        } else {
            self.rate_limiter.check()
        };

        // Clone service and rate limiter for async block
        let service = self.service.clone();

        Box::pin(async move {
            // Check rate limit
            if let Err(_) = rate_limit_result {
                return Err(actix_web::error::ErrorTooManyRequests("Rate limit exceeded"));
            }

            // Add security headers
            let mut response = service.call(req).await?;

            // Add security headers to the response
            let headers = response.headers_mut();

            // Content Security Policy
            headers.insert(
                header::CONTENT_SECURITY_POLICY,
                HeaderValue::from_static("default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://res.cloudinary.com; font-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com; connect-src 'self'; frame-src 'none'; object-src 'none'; base-uri 'self';"),
            );

            // X-Content-Type-Options
            headers.insert(
                header::X_CONTENT_TYPE_OPTIONS,
                HeaderValue::from_static("nosniff"),
            );

            // X-Frame-Options
            headers.insert(
                header::X_FRAME_OPTIONS,
                HeaderValue::from_static("DENY"),
            );

            // X-XSS-Protection
            headers.insert(
                HeaderName::from_static("x-xss-protection"),
                HeaderValue::from_static("1; mode=block"),
            );

            // Strict-Transport-Security
            headers.insert(
                header::STRICT_TRANSPORT_SECURITY,
                HeaderValue::from_static("max-age=31536000; includeSubDomains; preload"),
            );

            // Referrer-Policy
            headers.insert(
                header::REFERRER_POLICY,
                HeaderValue::from_static("same-origin"),
            );

            // Permissions-Policy (formerly Feature-Policy)
            headers.insert(
                HeaderName::from_static("permissions-policy"),
                HeaderValue::from_static("geolocation=(), camera=(), microphone=(), payment=(self)"),
            );

            Ok(response)
        })
    }
}

/// Configure security middleware for the application
pub fn configure_security() -> SecurityMiddleware {
    SecurityMiddleware::new()
}

/// Enhanced security middleware function
pub async fn enhanced_security_middleware(
    req: HttpRequest,
    payload: web::Payload,
) -> Result<HttpRequest, Error> {
    // This is a simplified version - in practice you'd implement enhanced security checks
    Ok(req)
}
