use bson::{doc, Document, oid::ObjectId};
use chrono::{DateTime, Utc, TimeZone};
use log;
use serde::{Serialize, Deserialize, Deserializer};
use serde::de::{self, Error as DeError};
use validator::Validate;

/// Currency enum
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "UPPERCASE")]
pub enum Currency {
    USD,
    EUR,
    GBP,
}

/// Account status enum
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum AccountStatus {
    Active,
    Inactive,
    Suspended,
    Closed,
}

/// Account type enum
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum AccountType {
    Savings,
    Current,
    Business,
}

impl ToString for AccountType {
    fn to_string(&self) -> String {
        match self {
            AccountType::Savings => "savings".to_string(),
            AccountType::Current => "current".to_string(),
            AccountType::Business => "business".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BalanceItem {
    #[serde(default)]
    pub currency: String,
    #[serde(default)]
    pub amount: f64,
    #[serde(default)]
    pub walletBalance: f64,
    #[serde(default)]
    pub cardBalance: f64,
}

/// Account model
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Account {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,

    pub userId: ObjectId,

    pub accountNumber: String,

    // Use a String for account_type to match the TypeScript model
    // This ensures compatibility with the MongoDB data
    #[serde(rename = "account_type")]
    pub account_type: String,

    #[serde(default)]
    pub username: Option<String>,

    #[serde(default)]
    pub balance: Vec<BalanceItem>,

    #[serde(default)]
    pub countries: Vec<String>,

    #[serde(default = "default_status")]
    pub status: String,

    #[serde(default)]
    pub transactions: Vec<ObjectId>,

    #[serde(default)]
    pub stripeCardId: Option<String>,

    #[serde(default)]
    pub stripeCardMetadata: Option<serde_json::Value>,

    #[serde(default)]
    pub creditCard: Option<serde_json::Value>,

    #[serde(default)]
    pub pin: Option<String>,

    #[serde(rename = "created_at", deserialize_with = "deserialize_flexible_date")]
    pub created_at: Option<DateTime<Utc>>,

    #[serde(rename = "updated_at", deserialize_with = "deserialize_flexible_date")]
    pub updated_at: Option<DateTime<Utc>>,
}

fn default_currency() -> String {
    "USD".to_string()
}

fn default_status() -> String {
    "active".to_string()
}

fn default_datetime() -> DateTime<Utc> {
    Utc::now()
}

/// Custom deserializer for DateTime fields that can handle both timestamp numbers and RFC 3339 strings
fn deserialize_flexible_date<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
where
    D: Deserializer<'de>,
{
    // This will capture any valid JSON value
    let value = serde_json::Value::deserialize(deserializer)?;

    match value {
        // If it's a string, try to parse it as an RFC 3339 date
        serde_json::Value::String(s) => {
            match DateTime::parse_from_rfc3339(&s) {
                Ok(dt) => Ok(Some(dt.with_timezone(&Utc))),
                Err(_) => {
                    log::warn!("Failed to parse date string: {}", s);
                    Ok(None)
                }
            }
        },
        // If it's a number, treat it as a timestamp in milliseconds
        serde_json::Value::Number(n) => {
            if let Some(ts) = n.as_f64() {
                // Convert milliseconds to seconds and nanoseconds
                let secs = (ts / 1000.0) as i64;
                let nsecs = ((ts % 1000.0) * 1_000_000.0) as u32;
                Ok(Some(Utc.timestamp_opt(secs, nsecs).unwrap()))
            } else {
                log::warn!("Failed to convert number to timestamp: {:?}", n);
                Ok(None)
            }
        },
        // For null or any other type, return None
        serde_json::Value::Null => Ok(None),
        _ => {
            log::warn!("Unexpected date format: {:?}", value);
            Ok(None)
        }
    }
}

impl Account {
    /// Check if account is active
    pub fn is_active(&self) -> bool {
        self.status == "active"
    }

    /// Get the account type as an enum
    pub fn get_account_type(&self) -> Option<AccountType> {
        match self.account_type.as_str() {
            "savings" => Some(AccountType::Savings),
            "current" => Some(AccountType::Current),
            "business" => Some(AccountType::Business),
            _ => {
                // Log the invalid account type for debugging
                log::error!("Invalid account type: {}", self.account_type);
                None
            }
        }
    }

    /// Get balance for a specific currency
    pub fn get_balance_for_currency(&self, currency: &str) -> Option<&BalanceItem> {
        self.balance.iter().find(|b| b.currency == currency)
    }

    /// Get balance amount for a specific currency
    pub fn get_balance_amount(&self, currency: &str) -> f64 {
        self.get_balance_for_currency(currency)
            .map(|b| b.amount)
            .unwrap_or(0.0)
    }

    /// Check if account has sufficient balance for a specific currency
    pub fn has_sufficient_balance(&self, amount: f64, currency: &str) -> bool {
        self.get_balance_amount(currency) >= amount
    }

    /// Credit account for a specific currency
    pub fn credit(&mut self, amount: f64, currency: &str) -> bool {
        if let Some(balance_item) = self.balance.iter_mut().find(|b| b.currency == currency) {
            balance_item.amount += amount;
            balance_item.walletBalance += amount;
            if let Some(updated_at) = &mut self.updated_at {
                *updated_at = Utc::now();
            } else {
                self.updated_at = Some(Utc::now());
            }
            true
        } else {
            // Currency not found, add a new balance item
            self.balance.push(BalanceItem {
                currency: currency.to_string(),
                amount,
                walletBalance: amount,
                cardBalance: 0.0,
            });
            if let Some(updated_at) = &mut self.updated_at {
                *updated_at = Utc::now();
            } else {
                self.updated_at = Some(Utc::now());
            }
            true
        }
    }

    /// Debit account for a specific currency
    pub fn debit(&mut self, amount: f64, currency: &str) -> bool {
        if let Some(balance_item) = self.balance.iter_mut().find(|b| b.currency == currency) {
            if balance_item.amount >= amount {
                balance_item.amount -= amount;
                balance_item.walletBalance -= amount;
                if let Some(updated_at) = &mut self.updated_at {
                    *updated_at = Utc::now();
                } else {
                    self.updated_at = Some(Utc::now());
                }
                true
            } else {
                false
            }
        } else {
            false
        }
    }
}

/// Payout account model
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PayoutAccount {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,

    pub user_id: ObjectId,

    #[validate(length(min = 1, max = 50, message = "Account name must be between 1 and 50 characters"))]
    pub account_name: String,

    pub account_type: String,

    pub bank_name: String,

    pub bank_code: String,

    pub account_number: String,

    pub routing_number: Option<String>,

    pub swift_code: Option<String>,

    pub iban: Option<String>,

    pub currency: Currency,

    pub country_code: String,

    pub is_verified: bool,

    pub is_default: bool,

    #[serde(deserialize_with = "deserialize_flexible_date")]
    pub created_at: Option<DateTime<Utc>>,

    #[serde(deserialize_with = "deserialize_flexible_date")]
    pub updated_at: Option<DateTime<Utc>>,
}

impl PayoutAccount {
    /// Create a new payout account
    pub fn new(
        user_id: ObjectId,
        account_name: String,
        account_type: String,
        bank_name: String,
        bank_code: String,
        account_number: String,
        currency: Currency,
        country_code: String,
    ) -> Self {
        let now = Utc::now();

        Self {
            id: None,
            user_id,
            account_name,
            account_type,
            bank_name,
            bank_code,
            account_number,
            routing_number: None,
            swift_code: None,
            iban: None,
            currency,
            country_code,
            is_verified: false,
            is_default: false,
            created_at: Some(now),
            updated_at: Some(now),
        }
    }

    /// Convert to BSON document
    pub fn to_document(&self) -> Document {
        bson::to_document(self).unwrap_or_else(|_| Document::new())
    }

    /// Create from BSON document
    pub fn from_document(doc: Document) -> Option<Self> {
        bson::from_document(doc).ok()
    }
}
