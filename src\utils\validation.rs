use regex::Regex;
use once_cell::sync::Lazy;

/// Validate amount for financial transactions
pub fn validate_amount(amount: f64) -> bool {
    amount > 0.0 && amount <= 1_000_000.0 && amount.is_finite()
}

/// Validate email address
pub fn validate_email(email: &str) -> bool {
    static EMAIL_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap()
    });
    EMAIL_REGEX.is_match(email)
}

/// Validate phone number (international format)
pub fn validate_phone_number(phone: &str) -> bool {
    static PHONE_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"^\+?[1-9]\d{1,14}$").unwrap()
    });
    PHONE_REGEX.is_match(phone)
}

/// Validate currency code (ISO 4217)
pub fn validate_currency_code(currency: &str) -> bool {
    const SUPPORTED_CURRENCIES: &[&str] = &["USD", "EUR", "GBP"];
    SUPPORTED_CURRENCIES.contains(&currency.to_uppercase().as_str())
}

/// Validate card number (basic Luhn algorithm)
pub fn validate_card_number(card_number: &str) -> bool {
    let digits: Vec<u32> = card_number
        .chars()
        .filter(|c| c.is_ascii_digit())
        .map(|c| c.to_digit(10).unwrap())
        .collect();

    if digits.len() < 13 || digits.len() > 19 {
        return false;
    }

    let mut sum = 0;
    let mut is_even = false;

    for &digit in digits.iter().rev() {
        let mut n = digit;
        if is_even {
            n *= 2;
            if n > 9 {
                n -= 9;
            }
        }
        sum += n;
        is_even = !is_even;
    }

    sum % 10 == 0
}

/// Validate CVV code
pub fn validate_cvv(cvv: &str) -> bool {
    static CVV_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"^\d{3,4}$").unwrap()
    });
    CVV_REGEX.is_match(cvv)
}

/// Validate expiry date (MM/YY format)
pub fn validate_expiry_date(expiry: &str) -> bool {
    static EXPIRY_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"^(0[1-9]|1[0-2])\/\d{2}$").unwrap()
    });
    
    if !EXPIRY_REGEX.is_match(expiry) {
        return false;
    }

    let parts: Vec<&str> = expiry.split('/').collect();
    if parts.len() != 2 {
        return false;
    }

    let month: u32 = parts[0].parse().unwrap_or(0);
    let year: u32 = parts[1].parse().unwrap_or(0);
    
    if month < 1 || month > 12 {
        return false;
    }

    // Check if the card is not expired
    let current_year = chrono::Utc::now().year() % 100;
    let current_month = chrono::Utc::now().month();
    
    if year as i32 > current_year {
        return true;
    }
    
    if year as i32 == current_year && month >= current_month {
        return true;
    }

    false
}

/// Validate postal code
pub fn validate_postal_code(postal_code: &str, country: &str) -> bool {
    match country.to_uppercase().as_str() {
        "US" => {
            static US_POSTAL_REGEX: Lazy<Regex> = Lazy::new(|| {
                Regex::new(r"^\d{5}(-\d{4})?$").unwrap()
            });
            US_POSTAL_REGEX.is_match(postal_code)
        }
        "CA" => {
            static CA_POSTAL_REGEX: Lazy<Regex> = Lazy::new(|| {
                Regex::new(r"^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$").unwrap()
            });
            CA_POSTAL_REGEX.is_match(postal_code)
        }
        "GB" => {
            static GB_POSTAL_REGEX: Lazy<Regex> = Lazy::new(|| {
                Regex::new(r"^[A-Za-z]{1,2}\d[A-Za-z\d]? \d[A-Za-z]{2}$").unwrap()
            });
            GB_POSTAL_REGEX.is_match(postal_code)
        }

        _ => {
            // Generic validation for other countries
            !postal_code.is_empty() && postal_code.len() <= 20
        }
    }
}

/// Validate country code (ISO 3166-1 alpha-2)
pub fn validate_country_code(country: &str) -> bool {
    const SUPPORTED_COUNTRIES: &[&str] = &[
        "US", "CA", "GB", "FR", "DE", "IT", "ES", "NL", "BE", "CH", "AT", "SE", "NO", "DK", "FI"
    ];
    SUPPORTED_COUNTRIES.contains(&country.to_uppercase().as_str())
}

/// Validate password strength
pub fn validate_password(password: &str) -> Result<(), Vec<String>> {
    let mut errors = Vec::new();

    if password.len() < 8 {
        errors.push("Password must be at least 8 characters long".to_string());
    }

    if password.len() > 128 {
        errors.push("Password must be no more than 128 characters long".to_string());
    }

    if !password.chars().any(|c| c.is_ascii_lowercase()) {
        errors.push("Password must contain at least one lowercase letter".to_string());
    }

    if !password.chars().any(|c| c.is_ascii_uppercase()) {
        errors.push("Password must contain at least one uppercase letter".to_string());
    }

    if !password.chars().any(|c| c.is_ascii_digit()) {
        errors.push("Password must contain at least one digit".to_string());
    }

    if !password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c)) {
        errors.push("Password must contain at least one special character".to_string());
    }

    if errors.is_empty() {
        Ok(())
    } else {
        Err(errors)
    }
}

/// Validate PIN (4-6 digits)
pub fn validate_pin(pin: &str) -> bool {
    static PIN_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"^\d{4,6}$").unwrap()
    });
    PIN_REGEX.is_match(pin)
}

/// Validate account number
pub fn validate_account_number(account_number: &str) -> bool {
    // Remove spaces and hyphens
    let cleaned = account_number.replace([' ', '-'], "");
    
    // Check length (typically 8-17 digits for bank accounts)
    if cleaned.len() < 8 || cleaned.len() > 17 {
        return false;
    }

    // Check if all characters are digits
    cleaned.chars().all(|c| c.is_ascii_digit())
}

/// Validate routing number (US)
pub fn validate_routing_number(routing_number: &str) -> bool {
    if routing_number.len() != 9 {
        return false;
    }

    if !routing_number.chars().all(|c| c.is_ascii_digit()) {
        return false;
    }

    // ABA routing number checksum validation
    let digits: Vec<u32> = routing_number
        .chars()
        .map(|c| c.to_digit(10).unwrap())
        .collect();

    let checksum = 3 * (digits[0] + digits[3] + digits[6])
        + 7 * (digits[1] + digits[4] + digits[7])
        + (digits[2] + digits[5] + digits[8]);

    checksum % 10 == 0
}

/// Validate IBAN (International Bank Account Number)
pub fn validate_iban(iban: &str) -> bool {
    let cleaned = iban.replace(' ', "").to_uppercase();
    
    if cleaned.len() < 15 || cleaned.len() > 34 {
        return false;
    }

    // Move first 4 characters to the end
    let rearranged = format!("{}{}", &cleaned[4..], &cleaned[0..4]);
    
    // Replace letters with numbers (A=10, B=11, ..., Z=35)
    let mut numeric_string = String::new();
    for c in rearranged.chars() {
        if c.is_ascii_digit() {
            numeric_string.push(c);
        } else if c.is_ascii_alphabetic() {
            let value = c as u32 - 'A' as u32 + 10;
            numeric_string.push_str(&value.to_string());
        } else {
            return false;
        }
    }

    // Calculate mod 97
    let mut remainder = 0u32;
    for c in numeric_string.chars() {
        remainder = (remainder * 10 + c.to_digit(10).unwrap()) % 97;
    }

    remainder == 1
}

/// Validate SWIFT/BIC code
pub fn validate_swift_code(swift: &str) -> bool {
    static SWIFT_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$").unwrap()
    });
    SWIFT_REGEX.is_match(&swift.to_uppercase())
}

/// Validate date of birth
pub fn validate_date_of_birth(day: u32, month: u32, year: u32) -> bool {
    if month < 1 || month > 12 {
        return false;
    }

    if day < 1 || day > 31 {
        return false;
    }

    // Check for valid day in month
    let days_in_month = match month {
        2 => {
            if is_leap_year(year) { 29 } else { 28 }
        }
        4 | 6 | 9 | 11 => 30,
        _ => 31,
    };

    if day > days_in_month {
        return false;
    }

    // Check minimum age (13 years)
    let current_year = chrono::Utc::now().year() as u32;
    let current_month = chrono::Utc::now().month();
    let current_day = chrono::Utc::now().day();

    let age = if month < current_month || (month == current_month && day <= current_day) {
        current_year - year
    } else {
        current_year - year - 1
    };

    age >= 13 && age <= 120
}

/// Check if a year is a leap year
fn is_leap_year(year: u32) -> bool {
    (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_amount() {
        assert!(validate_amount(100.0));
        assert!(validate_amount(0.01));
        assert!(!validate_amount(0.0));
        assert!(!validate_amount(-100.0));
        assert!(!validate_amount(f64::INFINITY));
        assert!(!validate_amount(f64::NAN));
    }

    #[test]
    fn test_validate_email() {
        assert!(validate_email("<EMAIL>"));
        assert!(validate_email("<EMAIL>"));
        assert!(!validate_email("invalid-email"));
        assert!(!validate_email("@domain.com"));
        assert!(!validate_email("user@"));
    }

    #[test]
    fn test_validate_phone_number() {
        assert!(validate_phone_number("+1234567890"));
        assert!(validate_phone_number("1234567890"));
        assert!(!validate_phone_number("123"));
        assert!(!validate_phone_number("abc123"));
    }

    #[test]
    fn test_validate_currency_code() {
        assert!(validate_currency_code("USD"));
        assert!(validate_currency_code("usd"));
        assert!(validate_currency_code("LRD"));
        assert!(!validate_currency_code("XXX"));
    }

    #[test]
    fn test_validate_pin() {
        assert!(validate_pin("1234"));
        assert!(validate_pin("123456"));
        assert!(!validate_pin("123"));
        assert!(!validate_pin("1234567"));
        assert!(!validate_pin("abcd"));
    }
}
