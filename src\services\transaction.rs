use std::sync::Arc;
use mongodb::{Database, bson::{doc, oid::ObjectId}};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use log::{error, info, warn};
use backoff::{ExponentialBackoff, future::retry};
use tokio::time::{sleep, Duration};
use futures_util::TryStreamExt;
use rand;

use crate::models::transaction::Transaction;
use crate::services::cache::CacheService;

/// Transaction types
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum TransactionType {
    #[serde(rename = "DEPOSIT")]
    Deposit,
    #[serde(rename = "WITHDRAWAL")]
    Withdrawal,
    #[serde(rename = "TRANSFER")]
    Transfer,
    #[serde(rename = "PAYMENT")]
    Payment,
    #[serde(rename = "REFUND")]
    Refund,
}

/// Transaction status
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum TransactionStatus {
    #[serde(rename = "PENDING")]
    Pending,
    #[serde(rename = "PROCESSING")]
    Processing,
    #[serde(rename = "COMPLETED")]
    Completed,
    #[serde(rename = "FAILED")]
    Failed,
    #[serde(rename = "CANCELLED")]
    Cancelled,
}

/// Transaction data for creation
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TransactionData {
    #[serde(rename = "userId")]
    pub user_id: ObjectId,
    pub amount: f64,
    pub currency: String,
    #[serde(rename = "type")]
    pub transaction_type: TransactionType,
    #[serde(rename = "sourceId")]
    pub source_id: Option<String>,
    #[serde(rename = "destinationId")]
    pub destination_id: Option<String>,
    pub reference: Option<String>,
    pub metadata: Option<serde_json::Value>,
}

/// Fraud check result
#[derive(Debug, Serialize, Deserialize)]
pub struct FraudCheckResult {
    pub flagged: bool,
    pub risk_score: f64,
    pub reason: Option<String>,
}

/// Transaction processing result
#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessingResult {
    pub success: bool,
    pub status: TransactionStatus,
}

/// Batch processing result
#[derive(Debug, Serialize, Deserialize)]
pub struct BatchProcessingResult {
    pub processed: usize,
    pub succeeded: usize,
    pub failed: usize,
}

/// Pagination info
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationInfo {
    pub total: u64,
    pub page: u32,
    pub limit: u32,
    pub pages: u32,
}

/// User transactions response
#[derive(Debug, Serialize, Deserialize)]
pub struct UserTransactionsResponse {
    pub transactions: Vec<Transaction>,
    pub pagination: PaginationInfo,
}

/// Service for handling financial transactions with high throughput
pub struct TransactionService {
    db: Arc<Database>,
    cache: Arc<CacheService>,
    transaction_lock_ttl: u64,
    transaction_cache_ttl: u64,
    batch_size: usize,
}

impl TransactionService {
    /// Create a new transaction service
    pub fn new(db: Arc<Database>, cache: Arc<CacheService>) -> Self {
        Self {
            db,
            cache,
            transaction_lock_ttl: 30,  // 30 seconds
            transaction_cache_ttl: 3600, // 1 hour
            batch_size: 100, // Process 100 transactions at a time
        }
    }

    /// Create a new transaction
    pub async fn create_transaction(&self, data: TransactionData) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        // Validate transaction data
        self.validate_transaction_data(&data)?;

        // Generate transaction ID
        let transaction_id = ObjectId::new();

        // Check for fraud (simplified implementation)
        let fraud_check_result = self.analyze_transaction(&data).await?;

        let now = chrono::Utc::now();
        let transaction = Transaction {
            id: Some(transaction_id),
            transaction_id: transaction_id.to_hex(),
            type_: match data.transaction_type {
                TransactionType::Transfer => crate::models::transaction::TransactionType::Transfer,
                TransactionType::Deposit => crate::models::transaction::TransactionType::Deposit,
                TransactionType::Withdrawal => crate::models::transaction::TransactionType::Withdrawal,
                TransactionType::Payment => crate::models::transaction::TransactionType::Transfer, // Map to Transfer since Payment doesn't exist in models
                TransactionType::Refund => crate::models::transaction::TransactionType::Refund,
            },
            title: self.generate_transaction_title(&data),
            amount: data.amount,
            currency: data.currency.clone(),
            status: if fraud_check_result.flagged {
                crate::models::transaction::TransactionStatus::Completed
            } else {
                crate::models::transaction::TransactionStatus::Pending
            },
            sender: crate::models::transaction::TransactionParticipant {
                user_id: data.user_id,
                name: "Unknown".to_string(),
                account_number: "Unknown".to_string(),
                username: None,
                email: None,
            },
            recipient: None,
            custom_description: None,
            predefined_description: "Transaction processed".to_string(),
            metadata: None,
            timestamp: now,
            processed_at: None,
            completed_at: None,
            card_transaction: None,
            receipt: None,
            reference: None,
            flagged: false,
            risk_score: None,
            ip_address: None,
            device_id: None,
            shard_key: rand::random::<u32>() % 100,
            fee: 0.0,
            payment_method: None,
            payment_processor: None,
            payment_processor_fee: None,
            payment_processor_reference: None,
            error_code: None,
            error_message: None,
            created_at: now,
            updated_at: now,
        };

        // Store transaction in database with retry logic
        let store_transaction = || async {
            self.db.collection::<Transaction>("transactions")
                .insert_one(&transaction, None)
                .await
        };

        retry(ExponentialBackoff::default(), store_transaction).await?;

        // Cache transaction for quick access
        let cache_key = format!("transaction:{}", transaction_id.to_hex());
        let _ = self.cache.set(&cache_key, &transaction, Some(self.transaction_cache_ttl)).await;

        // Add to processing queue if not flagged
        if !fraud_check_result.flagged {
            self.add_to_processing_queue(&transaction_id.to_hex()).await?;
        }

        info!("Transaction created: {}", transaction_id.to_hex());
        Ok(transaction_id.to_hex())
    }

    /// Process a transaction
    pub async fn process_transaction(&self, transaction_id: &str) -> Result<ProcessingResult, Box<dyn std::error::Error + Send + Sync>> {
        // Acquire lock to prevent concurrent processing
        let lock_acquired = self.acquire_transaction_lock(transaction_id).await?;
        if !lock_acquired {
            warn!("Failed to acquire lock for transaction {}", transaction_id);
            return Ok(ProcessingResult {
                success: false,
                status: TransactionStatus::Processing,
            });
        }

        // Get transaction from cache or database
        let transaction = match self.get_transaction(transaction_id).await? {
            Some(txn) => txn,
            None => {
                error!("Transaction {} not found", transaction_id);
                self.release_transaction_lock(transaction_id).await?;
                return Ok(ProcessingResult {
                    success: false,
                    status: TransactionStatus::Failed,
                });
            }
        };

        // Update status to processing
        self.update_transaction_status(transaction_id, &TransactionStatus::Processing).await?;

        // Process based on transaction type
        let success = match transaction.type_.to_string().as_str() {
            "DEPOSIT" => self.process_deposit(&transaction).await?,
            "WITHDRAWAL" => self.process_withdrawal(&transaction).await?,
            "TRANSFER" => self.process_transfer(&transaction).await?,
            "PAYMENT" => self.process_payment(&transaction).await?,
            "REFUND" => self.process_refund(&transaction).await?,
            _ => {
                error!("Unknown transaction type: {:?}", transaction.type_);
                false
            }
        };

        // Update final status
        let final_status = if success {
            TransactionStatus::Completed
        } else {
            TransactionStatus::Failed
        };

        self.update_transaction_status(transaction_id, &final_status).await?;

        // Release lock
        self.release_transaction_lock(transaction_id).await?;

        Ok(ProcessingResult {
            success,
            status: final_status,
        })
    }

    /// Process transactions in batch for high throughput
    pub async fn process_batch_transactions(&self) -> Result<BatchProcessingResult, Box<dyn std::error::Error + Send + Sync>> {
        // Get pending transactions from queue
        let pending_transactions = self.get_pending_transactions_from_queue(self.batch_size).await?;

        if pending_transactions.is_empty() {
            return Ok(BatchProcessingResult {
                processed: 0,
                succeeded: 0,
                failed: 0,
            });
        }

        info!("Processing batch of {} transactions", pending_transactions.len());

        // Process transactions in parallel with concurrency limit
        let mut handles = Vec::new();
        for transaction_id in &pending_transactions {
            let service = self.clone();
            let txn_id = transaction_id.clone();
            let handle = tokio::spawn(async move {
                service.process_transaction(&txn_id).await
            });
            handles.push(handle);
        }

        // Wait for all to complete
        let results = futures::future::join_all(handles).await;

        // Count results
        let mut succeeded = 0;
        let mut failed = 0;

        for result in results {
            match result {
                Ok(Ok(processing_result)) => {
                    if processing_result.success {
                        succeeded += 1;
                    } else {
                        failed += 1;
                    }
                }
                _ => failed += 1,
            }
        }

        info!("Batch processing completed: {} succeeded, {} failed", succeeded, failed);

        Ok(BatchProcessingResult {
            processed: pending_transactions.len(),
            succeeded,
            failed,
        })
    }

    /// Get transaction by ID
    pub async fn get_transaction(&self, transaction_id: &str) -> Result<Option<Transaction>, Box<dyn std::error::Error + Send + Sync>> {
        // Try to get from cache first
        let cache_key = format!("transaction:{}", transaction_id);
        if let Ok(Some(cached_transaction)) = self.cache.get::<Transaction>(&cache_key).await {
            return Ok(Some(cached_transaction));
        }

        // If not in cache, get from database
        let object_id = ObjectId::parse_str(transaction_id)?;
        let find_transaction = || async {
            self.db.collection::<Transaction>("transactions")
                .find_one(doc! { "_id": object_id }, None)
                .await
        };

        let transaction = retry(ExponentialBackoff::default(), find_transaction).await?;

        if let Some(ref txn) = transaction {
            // Cache for future requests
            let _ = self.cache.set(&cache_key, txn, Some(self.transaction_cache_ttl)).await;
        }

        Ok(transaction)
    }

    /// Get transactions for a user
    pub async fn get_user_transactions(
        &self,
        user_id: ObjectId,
        page: u32,
        limit: u32,
    ) -> Result<UserTransactionsResponse, Box<dyn std::error::Error + Send + Sync>> {
        let skip = ((page - 1) * limit) as u64;

        // Get total count (use cached value if available)
        let cache_key = format!("user:{}:transactionCount", user_id.to_hex());
        let total_count = match self.cache.get::<u64>(&cache_key).await {
            Ok(Some(count)) => count,
            _ => {
                let count_transactions = || async {
                    self.db.collection::<Transaction>("transactions")
                        .count_documents(doc! { "sender.userId": user_id }, None)
                        .await
                };

                let count = retry(ExponentialBackoff::default(), count_transactions).await?;
                let _ = self.cache.set(&cache_key, &count, Some(300)).await; // Cache for 5 minutes
                count
            }
        };

        // Get transactions with pagination
        let find_transactions = || async {
            let mut find_options = mongodb::options::FindOptions::default();
            find_options.skip = Some(skip);
            find_options.limit = Some(limit as i64);
            find_options.sort = Some(doc! { "createdAt": -1 });

            let cursor = self.db.collection::<Transaction>("transactions")
                .find(doc! { "sender.userId": user_id }, find_options)
                .await?;

            cursor.try_collect().await
        };

        let transactions = retry(ExponentialBackoff::default(), find_transactions).await?;

        Ok(UserTransactionsResponse {
            transactions,
            pagination: PaginationInfo {
                total: total_count,
                page,
                limit,
                pages: ((total_count as f64) / (limit as f64)).ceil() as u32,
            },
        })
    }

    // Private helper methods

    /// Validate transaction data
    fn validate_transaction_data(&self, data: &TransactionData) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        if data.amount <= 0.0 {
            return Err("Amount must be greater than 0".into());
        }

        if data.currency.is_empty() {
            return Err("Currency is required".into());
        }

        // Additional validation based on transaction type
        match data.transaction_type {
            TransactionType::Transfer => {
                if data.source_id.is_none() {
                    return Err("Source ID is required for transfers".into());
                }
                if data.destination_id.is_none() {
                    return Err("Destination ID is required for transfers".into());
                }
            }
            TransactionType::Payment => {
                if data.source_id.is_none() {
                    return Err("Source ID is required for payments".into());
                }
                if data.destination_id.is_none() {
                    return Err("Destination ID is required for payments".into());
                }
            }
            TransactionType::Refund => {
                if data.reference.is_none() {
                    return Err("Reference transaction is required for refunds".into());
                }
            }
            _ => {}
        }

        Ok(())
    }

    /// Analyze transaction for fraud (simplified implementation)
    async fn analyze_transaction(&self, data: &TransactionData) -> Result<FraudCheckResult, Box<dyn std::error::Error + Send + Sync>> {
        // Simplified fraud detection logic
        let mut risk_score = 0.0;
        let mut flagged = false;
        let mut reason = None;

        // Check for high amounts
        if data.amount > 10000.0 {
            risk_score += 0.3;
        }

        // Check for unusual patterns (simplified)
        if data.amount > 50000.0 {
            risk_score += 0.5;
            flagged = true;
            reason = Some("High amount transaction".to_string());
        }

        // Simulate processing time
        sleep(Duration::from_millis(10)).await;

        Ok(FraudCheckResult {
            flagged,
            risk_score,
            reason,
        })
    }

    /// Generate transaction title
    fn generate_transaction_title(&self, data: &TransactionData) -> String {
        match data.transaction_type {
            TransactionType::Deposit => "Account Deposit".to_string(),
            TransactionType::Withdrawal => "Account Withdrawal".to_string(),
            TransactionType::Transfer => "Account Transfer".to_string(),
            TransactionType::Payment => "Payment".to_string(),
            TransactionType::Refund => "Refund".to_string(),
        }
    }

    /// Generate transaction description
    fn generate_transaction_description(&self, data: &TransactionData) -> String {
        match data.transaction_type {
            TransactionType::Deposit => format!("Deposit of {} {}", data.amount, data.currency),
            TransactionType::Withdrawal => format!("Withdrawal of {} {}", data.amount, data.currency),
            TransactionType::Transfer => format!("Transfer of {} {}", data.amount, data.currency),
            TransactionType::Payment => format!("Payment of {} {}", data.amount, data.currency),
            TransactionType::Refund => format!("Refund of {} {}", data.amount, data.currency),
        }
    }

    /// Update transaction status
    async fn update_transaction_status(
        &self,
        transaction_id: &str,
        status: &TransactionStatus,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let object_id = ObjectId::parse_str(transaction_id)?;
        let now = Utc::now();

        // Update in database
        let update_db = || async {
            self.db.collection::<Transaction>("transactions")
                .update_one(
                    doc! { "_id": object_id },
                    doc! {
                        "$set": {
                            "status": bson::to_bson(&status).unwrap(),
                            "updatedAt": now,
                            "completedAt": if matches!(status, TransactionStatus::Completed) {
                                Some(now)
                            } else {
                                None
                            }
                        }
                    },
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_db).await?;

        // Update in cache
        let cache_key = format!("transaction:{}", transaction_id);
        if let Ok(Some(mut cached_transaction)) = self.cache.get::<Transaction>(&cache_key).await {
            cached_transaction.status = match status {
                TransactionStatus::Pending => crate::models::transaction::TransactionStatus::Pending,
                TransactionStatus::Processing => crate::models::transaction::TransactionStatus::Processing,
                TransactionStatus::Completed => crate::models::transaction::TransactionStatus::Completed,
                TransactionStatus::Failed => crate::models::transaction::TransactionStatus::Failed,
                TransactionStatus::Cancelled => crate::models::transaction::TransactionStatus::Cancelled,
            };
            cached_transaction.updated_at = now;
            if matches!(status, TransactionStatus::Completed) {
                cached_transaction.completed_at = Some(now);
            }
            let _ = self.cache.set(&cache_key, &cached_transaction, Some(self.transaction_cache_ttl)).await;
        }

        Ok(())
    }

    /// Acquire transaction lock
    async fn acquire_transaction_lock(&self, transaction_id: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let lock_key = format!("lock:transaction:{}", transaction_id);
        match self.cache.set(&lock_key, &1, Some(self.transaction_lock_ttl)).await {
            Ok(success) => Ok(success),
            Err(_) => Ok(false),
        }
    }

    /// Release transaction lock
    async fn release_transaction_lock(&self, transaction_id: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let lock_key = format!("lock:transaction:{}", transaction_id);
        match self.cache.delete(&lock_key).await {
            Ok(success) => Ok(success),
            Err(_) => Ok(false),
        }
    }

    /// Add transaction to processing queue
    async fn add_to_processing_queue(&self, transaction_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let queue_key = format!("queue:pendingTransactions:{}", transaction_id);
        let timestamp = Utc::now().timestamp();
        let _ = self.cache.set(&queue_key, &timestamp, Some(86400)).await; // 24 hours TTL
        Ok(())
    }

    /// Get pending transactions from queue
    async fn get_pending_transactions_from_queue(&self, limit: usize) -> Result<Vec<String>, Box<dyn std::error::Error + Send + Sync>> {
        // Get pending transactions from database
        let find_pending = || async {
            let mut find_options = mongodb::options::FindOptions::default();
            find_options.sort = Some(doc! { "createdAt": 1 });
            find_options.limit = Some(limit as i64);

            let cursor = self.db.collection::<Transaction>("transactions")
                .find(doc! { "status": "PENDING" }, find_options)
                .await?;

            cursor.try_collect::<Vec<Transaction>>().await
        };

        let pending_transactions = retry(ExponentialBackoff::default(), find_pending).await?;

        Ok(pending_transactions
            .into_iter()
            .filter_map(|t| t.id.map(|id| id.to_hex()))
            .collect())
    }

    // Transaction processing methods
    // These contain the actual business logic for each transaction type

    /// Process deposit transaction
    async fn process_deposit(&self, _transaction: &Transaction) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Implementation would include:
        // 1. Verify deposit source
        // 2. Update user balance
        // 3. Create ledger entries
        // 4. Send notifications

        // Simulate processing time
        sleep(Duration::from_millis(50)).await;

        // For demo purposes, return success
        Ok(true)
    }

    /// Process withdrawal transaction
    async fn process_withdrawal(&self, _transaction: &Transaction) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Implementation would include:
        // 1. Check sufficient balance
        // 2. Reserve funds
        // 3. Process external withdrawal
        // 4. Update user balance
        // 5. Create ledger entries
        // 6. Send notifications

        // Simulate processing time
        sleep(Duration::from_millis(50)).await;

        // For demo purposes, return success
        Ok(true)
    }

    /// Process transfer transaction
    async fn process_transfer(&self, _transaction: &Transaction) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Implementation would include:
        // 1. Check sufficient balance
        // 2. Update source balance
        // 3. Update destination balance
        // 4. Create ledger entries
        // 5. Send notifications

        // Simulate processing time
        sleep(Duration::from_millis(50)).await;

        // For demo purposes, return success
        Ok(true)
    }

    /// Process payment transaction
    async fn process_payment(&self, _transaction: &Transaction) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Implementation would include:
        // 1. Check sufficient balance
        // 2. Process payment gateway
        // 3. Update balances
        // 4. Create ledger entries
        // 5. Send notifications

        // Simulate processing time
        sleep(Duration::from_millis(50)).await;

        // For demo purposes, return success
        Ok(true)
    }

    /// Process refund transaction
    async fn process_refund(&self, _transaction: &Transaction) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Implementation would include:
        // 1. Verify original transaction
        // 2. Process refund
        // 3. Update balances
        // 4. Create ledger entries
        // 5. Send notifications

        // Simulate processing time
        sleep(Duration::from_millis(50)).await;

        // For demo purposes, return success
        Ok(true)
    }

    /// Get transaction statistics
    pub async fn get_transaction_stats(&self) -> Result<TransactionStats, Box<dyn std::error::Error + Send + Sync>> {
        let get_stats = || async {
            let collection = self.db.collection::<Transaction>("transactions");

            // Count transactions by status
            let pipeline = vec![
                doc! { "$group": { "_id": "$status", "count": { "$sum": 1 } } }
            ];

            let mut cursor = collection.aggregate(pipeline, None).await?;
            let mut status_counts = std::collections::HashMap::new();

            while cursor.advance().await? {
                let doc = cursor.current();
                if let (Ok(status), Ok(count)) = (
                    doc.get_str("_id"),
                    doc.get_i32("count")
                ) {
                    status_counts.insert(status.to_string(), count as u64);
                }
            }

            // Get total count
            let total_transactions = collection.count_documents(doc! {}, None).await?;

            Ok::<TransactionStats, mongodb::error::Error>(TransactionStats {
                total_transactions,
                status_counts,
            })
        };

        retry(ExponentialBackoff::default(), get_stats).await
    }
}

impl Clone for TransactionService {
    fn clone(&self) -> Self {
        Self {
            db: self.db.clone(),
            cache: self.cache.clone(),
            transaction_lock_ttl: self.transaction_lock_ttl,
            transaction_cache_ttl: self.transaction_cache_ttl,
            batch_size: self.batch_size,
        }
    }
}

/// Transaction statistics
#[derive(Debug, Serialize, Deserialize)]
pub struct TransactionStats {
    pub total_transactions: u64,
    pub status_counts: std::collections::HashMap<String, u64>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_transaction_type_serialization() {
        let deposit = TransactionType::Deposit;
        let serialized = serde_json::to_string(&deposit).unwrap();
        assert_eq!(serialized, "\"DEPOSIT\"");

        let deserialized: TransactionType = serde_json::from_str(&serialized).unwrap();
        assert_eq!(deserialized, TransactionType::Deposit);
    }

    #[test]
    fn test_transaction_status_serialization() {
        let pending = TransactionStatus::Pending;
        let serialized = serde_json::to_string(&pending).unwrap();
        assert_eq!(serialized, "\"PENDING\"");

        let deserialized: TransactionStatus = serde_json::from_str(&serialized).unwrap();
        assert_eq!(deserialized, TransactionStatus::Pending);
    }

    #[tokio::test]
    async fn test_transaction_data_validation() {
        let service = TransactionService::new(
            Arc::new(mongodb::Database::new(
                mongodb::Client::with_uri_str("mongodb://localhost:27017").unwrap(),
                "test"
            )),
            Arc::new(CacheService::new_memory_only())
        );

        // Valid transaction data
        let valid_data = TransactionData {
            user_id: ObjectId::new(),
            amount: 100.0,
            currency: "USD".to_string(),
            transaction_type: TransactionType::Deposit,
            source_id: None,
            destination_id: None,
            reference: None,
            metadata: None,
        };

        assert!(service.validate_transaction_data(&valid_data).is_ok());

        // Invalid transaction data (zero amount)
        let invalid_data = TransactionData {
            amount: 0.0,
            ..valid_data.clone()
        };

        assert!(service.validate_transaction_data(&invalid_data).is_err());

        // Invalid transfer data (missing source)
        let invalid_transfer = TransactionData {
            transaction_type: TransactionType::Transfer,
            source_id: None,
            destination_id: Some("dest123".to_string()),
            ..valid_data
        };

        assert!(service.validate_transaction_data(&invalid_transfer).is_err());
    }
}
