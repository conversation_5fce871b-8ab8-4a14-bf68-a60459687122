use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};

use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;

use crate::services::cache::CacheService;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub timestamp: u64,
    pub requests: RequestMetrics,
    pub database: DatabaseMetrics,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RequestMetrics {
    pub total: u64,
    pub success: u64,
    pub error: u64,
    pub avg_response_time: f64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DatabaseMetrics {
    pub connections: u32,
    pub queries: u64,
    pub avg_query_time: f64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AlertConfig {
    pub cpu_threshold: f32,
    pub memory_threshold: f64,
    pub response_time_threshold: f64,
    pub error_rate_threshold: f64,
}

impl Default for AlertConfig {
    fn default() -> Self {
        Self {
            cpu_threshold: 80.0,
            memory_threshold: 85.0,
            response_time_threshold: 1000.0, // 1 second
            error_rate_threshold: 5.0, // 5%
        }
    }
}

pub struct MonitoringService {
    metrics: Arc<RwLock<PerformanceMetrics>>,
    system: Arc<RwLock<System>>,
    cache_service: Arc<CacheService>,
    alert_config: AlertConfig,
    start_time: Instant,
    request_times: Arc<RwLock<Vec<Duration>>>,
    alert_sender: Option<mpsc::UnboundedSender<String>>,
}

impl MonitoringService {
    pub fn new(cache_service: Arc<CacheService>) -> Self {
        let mut system = System::new_all();
        system.refresh_all();

        let initial_metrics = PerformanceMetrics {
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            cpu: CpuMetrics {
                usage: 0.0,
                load_avg: vec![0.0, 0.0, 0.0],
            },
            memory: MemoryMetrics {
                total: system.total_memory(),
                free: system.free_memory(),
                used: system.used_memory(),
                used_percent: (system.used_memory() as f64 / system.total_memory() as f64) * 100.0,
            },
            process: ProcessMetrics {
                uptime: 0,
                memory_usage: 0,
                cpu_usage: 0.0,
            },
            requests: RequestMetrics {
                total: 0,
                success: 0,
                error: 0,
                avg_response_time: 0.0,
            },
            database: DatabaseMetrics {
                connections: 0,
                queries: 0,
                avg_query_time: 0.0,
            },
        };

        Self {
            metrics: Arc::new(RwLock::new(initial_metrics)),
            system: Arc::new(RwLock::new(system)),
            cache_service,
            alert_config: AlertConfig::default(),
            start_time: Instant::now(),
            request_times: Arc::new(RwLock::new(Vec::new())),
            alert_sender: None,
        }
    }

    pub async fn get_metrics(&self) -> PerformanceMetrics {
        self.metrics.read().await.clone()
    }
}