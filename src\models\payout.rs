use chrono::{DateTime, Utc};
use mongodb::bson::{doc, oid::ObjectId, Document};
use serde::{Deserialize, Serialize};
use validator::Validate;

use crate::utils::date_utils::deserialize_flexible_date;

/// Payout status enum
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum PayoutStatus {
    Pending,
    Processing,
    Completed,
    Failed,
    Cancelled,
    Refunded,
}

impl Default for PayoutStatus {
    fn default() -> Self {
        PayoutStatus::Pending
    }
}

/// Supported currencies
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Currency {
    USD,
}

impl Default for Currency {
    fn default() -> Self {
        Currency::USD
    }
}

/// Payout destination details
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PayoutDestination {
    #[validate(length(min = 1, max = 100, message = "Account name must be between 1 and 100 characters"))]
    pub account_name: String,
    
    #[validate(length(min = 1, max = 50, message = "Account number must be between 1 and 50 characters"))]
    pub account_number: String,
    
    pub bank_name: String,
    pub bank_code: String,
    pub routing_number: Option<String>,
    pub swift_code: Option<String>,
    pub iban: Option<String>,
    pub country_code: String,
}

/// Processor response details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessorResponse {
    pub provider: String,
    pub id: String,
    pub status: String,
    pub amount: f64,
    pub currency: String,
    pub created: DateTime<Utc>,
    pub arrival_date: Option<DateTime<Utc>>,
    pub metadata: Option<Document>,
}

/// Fee breakdown
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeeBreakdown {
    pub base_fee: f64,
    pub percentage_fee: f64,
    pub total_fee: f64,
    pub net_amount: f64,
}

/// Payout model
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Payout {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,

    pub user_id: ObjectId,

    #[validate(range(min = 0.01, message = "Amount must be greater than 0"))]
    pub amount: f64,

    pub currency: Currency,

    pub destination: PayoutDestination,

    pub status: PayoutStatus,

    pub processor: String, // "stripe", "flutterwave", etc.

    pub processor_response: Option<ProcessorResponse>,

    pub fee_breakdown: FeeBreakdown,

    pub net_amount: f64,

    pub note: Option<String>,

    pub reference: String,

    pub metadata: Option<Document>,

    #[serde(deserialize_with = "deserialize_flexible_date")]
    pub created_at: Option<DateTime<Utc>>,

    #[serde(deserialize_with = "deserialize_flexible_date")]
    pub updated_at: Option<DateTime<Utc>>,

    #[serde(deserialize_with = "deserialize_flexible_date")]
    pub processed_at: Option<DateTime<Utc>>,

    #[serde(deserialize_with = "deserialize_flexible_date")]
    pub completed_at: Option<DateTime<Utc>>,
}

impl Default for Payout {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            id: None,
            user_id: ObjectId::new(),
            amount: 0.0,
            currency: Currency::default(),
            destination: PayoutDestination {
                account_name: String::new(),
                account_number: String::new(),
                bank_name: String::new(),
                bank_code: String::new(),
                routing_number: None,
                swift_code: None,
                iban: None,
                country_code: String::new(),
            },
            status: PayoutStatus::default(),
            processor: String::new(),
            processor_response: None,
            fee_breakdown: FeeBreakdown {
                base_fee: 0.0,
                percentage_fee: 0.0,
                total_fee: 0.0,
                net_amount: 0.0,
            },
            net_amount: 0.0,
            note: None,
            reference: String::new(),
            metadata: None,
            created_at: Some(now),
            updated_at: Some(now),
            processed_at: None,
            completed_at: None,
        }
    }
}

/// Payout account model
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PayoutAccount {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,

    pub user_id: ObjectId,

    #[validate(length(min = 1, max = 50, message = "Account name must be between 1 and 50 characters"))]
    pub account_name: String,

    pub account_type: String,

    pub bank_name: String,

    pub bank_code: String,

    pub account_number: String,

    pub routing_number: Option<String>,

    pub swift_code: Option<String>,

    pub iban: Option<String>,

    pub currency: Currency,

    pub country_code: String,

    pub is_verified: bool,

    pub is_default: bool,

    #[serde(deserialize_with = "deserialize_flexible_date")]
    pub created_at: Option<DateTime<Utc>>,

    #[serde(deserialize_with = "deserialize_flexible_date")]
    pub updated_at: Option<DateTime<Utc>>,
}

impl From<PayoutAccount> for PayoutDestination {
    fn from(account: PayoutAccount) -> Self {
        Self {
            account_name: account.account_name,
            account_number: account.account_number,
            bank_name: account.bank_name,
            bank_code: account.bank_code,
            routing_number: account.routing_number,
            swift_code: account.swift_code,
            iban: account.iban,
            country_code: account.country_code,
        }
    }
}

impl Default for PayoutAccount {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            id: None,
            user_id: ObjectId::new(),
            account_name: String::new(),
            account_type: String::new(),
            bank_name: String::new(),
            bank_code: String::new(),
            account_number: String::new(),
            routing_number: None,
            swift_code: None,
            iban: None,
            currency: Currency::default(),
            country_code: String::new(),
            is_verified: false,
            is_default: false,
            created_at: Some(now),
            updated_at: Some(now),
        }
    }
}

/// Payout request DTO
#[derive(Debug, Deserialize, Validate)]
pub struct PayoutRequest {
    #[validate(range(min = 0.01, message = "Amount must be greater than 0"))]
    pub amount: f64,
    
    pub currency: String,
    
    #[validate(length(min = 1, message = "Destination is required"))]
    pub destination: String, // Payout account ID
    
    pub note: Option<String>,
    
    pub metadata: Option<Document>,
}

/// Payout response DTO
#[derive(Debug, Serialize)]
pub struct PayoutResponse {
    pub status: bool,
    pub message: String,
    pub data: Option<Payout>,
    pub meta: Option<serde_json::Value>,
}

impl PayoutResponse {
    pub fn success(data: Payout) -> Self {
        Self {
            status: true,
            message: "PAYOUT_CREATED_SUCCESSFULLY".to_string(),
            data: Some(data),
            meta: None,
        }
    }

    pub fn error(message: &str, meta: Option<serde_json::Value>) -> Self {
        Self {
            status: false,
            message: message.to_string(),
            data: None,
            meta,
        }
    }
}

/// Constants
pub const PAYOUT_STATUSES: &[&str] = &["pending", "processing", "completed", "failed", "cancelled", "refunded"];
pub const SUPPORTED_CURRENCIES: &[&str] = &["USD"];
