cargo :    Compiling sangapay v0.1.0 
(D:\workspace\.upwork\sangapay\sangapay-rust)
At line:1 char:1
+ cargo build 2>&1 | tee build_log_clean.txt
+ ~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (   Compiling s 
   a...\sangapay-rust):String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
error[E0252]: the name `<PERSON>rror` is defined multiple times
  --> src\middleware\security.rs:16:35
   |
3  |     Error, HttpMessage,
   |     ----- previous import of the type `Error` here
...
16 | use actix_web::{HttpRequest, web, Error};
   |                                   ^^^^^ `Error` 
reimported here
   |
   = note: `Error` must be defined only once in the type 
namespace of this module

error[E0252]: the name `TryStreamExt` is defined multiple 
times
  --> src\services\transaction.rs:10:5
   |
8  | use futures::TryStreamExt;
   |     --------------------- previous import of the trait 
`TryStreamExt` here
9  | use rand;
10 | use futures_util::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ `TryStreamExt` 
reimported here
   |
   = note: `TryStreamExt` must be defined only once in the 
type namespace of this module

error: environment variable `BUILD_TIME` not defined at 
compile time
   --> src\controllers\health.rs:296:25
    |
296 |             build_time: 
env!("BUILD_TIME").unwrap_or("unknown").to_string(),
    |                         ^^^^^^^^^^^^^^^^^^
    |
    = help: use `std::env::var("BUILD_TIME")` to read the 
variable at run time
    = note: this error originates in the macro `env` (in 
Nightly builds, run with -Z macro-backtrace for more info)

error: environment variable `GIT_COMMIT` not defined at 
compile time
   --> src\controllers\health.rs:297:25
    |
297 |             git_commit: 
env!("GIT_COMMIT").unwrap_or("unknown").to_string(),
    |                         ^^^^^^^^^^^^^^^^^^
    |
    = help: use `std::env::var("GIT_COMMIT")` to read the 
variable at run time
    = note: this error originates in the macro `env` (in 
Nightly builds, run with -Z macro-backtrace for more info)

error[E0432]: unresolved import `crate::middleware::oauth`
 --> src\routes\developer.rs:5:24
  |
5 | use crate::middleware::oauth::oauth_auth;
  |                        ^^^^^ could not find `oauth` in 
`middleware`

error[E0432]: unresolved import `backoff::future`
   --> src\services\account_number.rs:7:35
    |
7   | use backoff::{ExponentialBackoff, future::retry};
    |                                   ^^^^^^ could not 
find `future` in `backoff`
    |
note: found an item that was configured out
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:223:9
    |
223 | pub mod future;
    |         ^^^^^^
note: the item is gated behind the `futures` feature
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:221:7
    |
221 | #[cfg(feature = "futures")]
    |       ^^^^^^^^^^^^^^^^^^^

error[E0432]: unresolved import `backoff::future`
   --> src\services\exchange.rs:6:35
    |
6   | use backoff::{ExponentialBackoff, future::retry};
    |                                   ^^^^^^ could not 
find `future` in `backoff`
    |
note: found an item that was configured out
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:223:9
    |
223 | pub mod future;
    |         ^^^^^^
note: the item is gated behind the `futures` feature
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:221:7
    |
221 | #[cfg(feature = "futures")]
    |       ^^^^^^^^^^^^^^^^^^^

error[E0432]: unresolved imports `sysinfo::SystemExt`, 
`sysinfo::CpuExt`, `sysinfo::ProcessExt`
 --> src\services\monitoring.rs:8:23
  |
8 | use sysinfo::{System, SystemExt, CpuExt, ProcessExt};
  |                       ^^^^^^^^^  ^^^^^^  ^^^^^^^^^^ no 
`ProcessExt` in the root
  |                       |          |
  |                       |          no `CpuExt` in the root
  |                       no `SystemExt` in the root
  |
help: a similar name exists in the module
  |
8 - use sysinfo::{System, SystemExt, CpuExt, ProcessExt};
8 + use sysinfo::{System, System, CpuExt, ProcessExt};
  |
help: a similar name exists in the module
  |
8 - use sysinfo::{System, SystemExt, CpuExt, ProcessExt};
8 + use sysinfo::{System, SystemExt, CpuExt, Process};
  |

error[E0432]: unresolved import `backoff::future`
   --> src\services\oauth.rs:6:35
    |
6   | use backoff::{ExponentialBackoff, future::retry};
    |                                   ^^^^^^ could not 
find `future` in `backoff`
    |
note: found an item that was configured out
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:223:9
    |
223 | pub mod future;
    |         ^^^^^^
note: the item is gated behind the `futures` feature
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:221:7
    |
221 | #[cfg(feature = "futures")]
    |       ^^^^^^^^^^^^^^^^^^^

error[E0432]: unresolved import `backoff::future`
   --> src\services\payout.rs:5:35
    |
5   | use backoff::{ExponentialBackoff, future::retry};
    |                                   ^^^^^^ could not 
find `future` in `backoff`
    |
note: found an item that was configured out
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:223:9
    |
223 | pub mod future;
    |         ^^^^^^
note: the item is gated behind the `futures` feature
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:221:7
    |
221 | #[cfg(feature = "futures")]
    |       ^^^^^^^^^^^^^^^^^^^

error[E0432]: unresolved imports `stripe::ListPagination`, 
`stripe::AccountCreateParams`, 
`stripe::AccountUpdateParams`, `stripe::BusinessType`, 
`stripe::Country`, `stripe::AccountPayouts`, 
`stripe::CardCreateParams`, `stripe::CardUpdateParams`, 
`stripe::CardStatus`, `stripe::CardShipping`, 
`stripe::CardSpendingControls`, 
`stripe::CardSpendingControlsSpendingLimit`, 
`stripe::CardSpendingControlsSpendingLimitInterval`, 
`stripe::CardAuthorizationControls`, 
`stripe::CardReplacementReason`, `stripe::Cardholder`, 
`stripe::CardholderCreateParams`, 
`stripe::CardholderUpdateParams`, `stripe::CardholderType`, 
`stripe::CardholderStatus`, `stripe::CardholderBilling`, 
`stripe::CardholderIndividual`, 
`stripe::CardholderCompany`, 
`stripe::IssuingAuthorizationListParams`, 
`stripe::IssuingAuthorizationUpdateParams`, 
`stripe::IssuingTransactionListParams`, 
`stripe::IssuingDisputeCreateParams`, 
`stripe::IssuingDisputeListParams`, 
`stripe::PaymentIntentCreateParams`, 
`stripe::PaymentMethodCreateParams`, 
`stripe::PaymentMethodAttachParams`, 
`stripe::PaymentMethodCard`, 
`stripe::PaymentMethodBillingDetails`, 
`stripe::ChargeCreateParams`, `stripe::ChargeUpdateParams`, 
`stripe::ChargeListParams`, `stripe::RefundCreateParams`, 
`stripe::RefundListParams`, `stripe::PayoutCreateParams`, 
`stripe::PayoutUpdateParams`, `stripe::PayoutListParams`, 
`stripe::PayoutStatus`, `stripe::TransferCreateParams`, 
`stripe::TransferListParams`, 
`stripe::TreasuryFinancialAccount`, 
`stripe::TreasuryFinancialAccountCreateParams`, 
`stripe::TreasuryFinancialAccountListParams`, 
`stripe::TreasuryFinancialAccountUpdateParams`, 
`stripe::TreasuryInboundTransfer`, 
`stripe::TreasuryOutboundTransfer`, 
`stripe::TreasuryReceivedCredit`, 
`stripe::TreasuryReceivedDebit`, 
`stripe::TreasuryTransaction`, 
`stripe::BalanceTransactionListParams`, 
`stripe::EventListParams`, `stripe::CustomerCreateParams`, 
`stripe::CustomerUpdateParams`, 
`stripe::CustomerListParams`, 
`stripe::ProductCreateParams`, `stripe::PriceCreateParams`, 
`stripe::SubscriptionCreateParams`, 
`stripe::FileCreateParams`, `stripe::DisputeListParams`, 
`stripe::DisputeUpdateParams`, `stripe::Terminal`, 
`stripe::TerminalReaderCreateParams`, 
`stripe::TerminalReaderListParams`, 
`stripe::TerminalLocationCreateParams`, 
`stripe::RadarEarlyFraudWarning`, `stripe::RadarValueList`, 
`stripe::RadarValueListCreateParams`, `stripe::ReportRun`, 
`stripe::ReportRunCreateParams`, `stripe::ReportType`, 
`stripe::ReportTypeListParams`, 
`stripe::InvoiceCreateParams`, 
`stripe::InvoiceItemCreateParams`, 
`stripe::SubscriptionScheduleCreateParams`, 
`stripe::UsageRecordCreateParams`, 
`stripe::PlanCreateParams`, `stripe::Tax`, 
`stripe::TaxCalculationCreateParams`, 
`stripe::TaxTransaction`, 
`stripe::TaxTransactionCreateParams`, 
`stripe::TaxRateCreateParams`, `stripe::TaxCodeListParams`, 
`stripe::Identity`, `stripe::IdentityVerificationSession`, 
`stripe::IdentityVerificationSessionCreateParams`, 
`stripe::IdentityVerificationReport`, 
`stripe::IdentityVerificationReportListParams`, 
`stripe::Climate`, `stripe::ClimateOrder`, 
`stripe::ClimateOrderCreateParams`, 
`stripe::ClimateProduct`, `stripe::ClimateSupplier`, 
`stripe::FinancialConnectionsAccount`, 
`stripe::FinancialConnectionsAccountListParams`, 
`stripe::FinancialConnectionsSession`, 
`stripe::FinancialConnectionsSessionCreateParams`, 
`stripe::SigmaScheduledQueryRun`, 
`stripe::SigmaScheduledQueryRunListParams`, 
`stripe::SetupIntentCreateParams`, 
`stripe::SetupIntentConfirmParams`, 
`stripe::SourceCreateParams`, `stripe::SourceUpdateParams`, 
`stripe::TokenCreateParams`, `stripe::CouponCreateParams`, 
`stripe::PromotionCodeCreateParams`, 
`stripe::ReviewListParams`, `stripe::ReviewApproveParams`, 
`stripe::TopupCreateParams`, `stripe::TopupListParams`, 
`stripe::MandateRetrieveParams`, 
`stripe::PaymentLinkCreateParams`, 
`stripe::PaymentLinkUpdateParams`, 
`stripe::CheckoutSessionCreateParams`, 
`stripe::CheckoutSessionListParams`, 
`stripe::QuoteCreateParams`, `stripe::QuoteListParams`, 
`stripe::QuoteFinalizeParams`, `stripe::TestClock`, 
`stripe::TestClockCreateParams`, 
`stripe::TestClockAdvanceParams`, `stripe::AppsSecret`, 
`stripe::AppsSecretCreateParams`
   --> src\services\stripe.rs:25:41
    |
25  |     Client, Currency, Expandable, List, 
ListPagination, Metadata,
    |                                         
^^^^^^^^^^^^^^ no `ListPagination` in the root
...
28  |     Account, AccountCreateParams, 
AccountUpdateParams, AccountType, BusinessType, Country,
    |              ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^ 
              ^^^^^^^^^^^^  ^^^^^^^ no `Country` in the root
    |              |                    |                   
              |
    |              |                    |                   
              no `BusinessType` in the root
    |              |                    no 
`AccountUpdateParams` in the root
    |              no `AccountCreateParams` in the root
29  |     AccountCapabilities, AccountSettings, 
AccountRequirements, AccountPayouts,
    |                                                       
         ^^^^^^^^^^^^^^ no `AccountPayouts` in the root
...
32  |     Card, CardCreateParams, CardUpdateParams, 
CardType, CardStatus, CardShipping,
    |           ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^          
  ^^^^^^^^^^  ^^^^^^^^^^^^ no `CardShipping` in the root
    |           |                 |                         
  |
    |           |                 |                         
  no `CardStatus` in the root
    |           |                 no `CardUpdateParams` in 
the root
    |           no `CardCreateParams` in the root
33  |     CardSpendingControls, 
CardSpendingControlsSpendingLimit, 
CardSpendingControlsSpendingLimitInterval,
    |     ^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
34  |     CardAuthorizationControls, CardReplacementReason,
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^
...
37  |     Cardholder, CardholderCreateParams, 
CardholderUpdateParams, CardholderType, CardholderStatus,
    |     ^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^
38  |     CardholderBilling, CardholderIndividual, 
CardholderCompany,
    |     ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^
...
41  |     IssuingAuthorization, 
IssuingAuthorizationListParams, 
IssuingAuthorizationUpdateParams,
    |                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
42  |     IssuingTransaction, IssuingTransactionListParams, 
IssuingTransactionType,
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
43  |     IssuingDispute, IssuingDisputeCreateParams, 
IssuingDisputeListParams,
    |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^
...
46  |     PaymentIntent, PaymentIntentCreateParams, 
PaymentIntentUpdateParams, PaymentIntentConfirmParams,
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^
47  |     PaymentIntentStatus, PaymentIntentCaptureMethod, 
PaymentIntentConfirmationMethod,
48  |     PaymentMethod, PaymentMethodCreateParams, 
PaymentMethodAttachParams, PaymentMethodType,
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^
49  |     PaymentMethodCard, PaymentMethodBillingDetails,
    |     ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
52  |     Charge, ChargeCreateParams, ChargeUpdateParams, 
ChargeListParams,
    |             ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^
53  |     Refund, RefundCreateParams, RefundListParams, 
RefundReason,
    |             ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^
...
56  |     Payout, PayoutCreateParams, PayoutUpdateParams, 
PayoutListParams, PayoutStatus,
    |             ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^
57  |     Transfer, TransferCreateParams, 
TransferListParams, TransferReversal,
    |               ^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^
...
60  |     TreasuryFinancialAccount, 
TreasuryFinancialAccountCreateParams, 
TreasuryFinancialAccountListParams,
    |     ^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
61  |     TreasuryFinancialAccountUpdateParams, 
TreasuryInboundTransfer, TreasuryOutboundTransfer,
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^
62  |     TreasuryReceivedCredit, TreasuryReceivedDebit, 
TreasuryTransaction,
    |     ^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^
...
65  |     Balance, BalanceTransaction, 
BalanceTransactionListParams,
    |                                  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
68  |     Event, EventListParams, EventType, EventObject, 
Webhook, WebhookEndpoint,
    |            ^^^^^^^^^^^^^^^
...
71  |     Customer, CustomerCreateParams, 
CustomerUpdateParams, CustomerListParams,
    |               ^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^
...
74  |     Product, ProductCreateParams, Price, 
PriceCreateParams,
    |              ^^^^^^^^^^^^^^^^^^^         
^^^^^^^^^^^^^^^^^
75  |     Subscription, SubscriptionCreateParams, 
SubscriptionItem,
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^
...
78  |     File, FileCreateParams, FilePurpose,
    |           ^^^^^^^^^^^^^^^^
...
81  |     Dispute, DisputeListParams, DisputeUpdateParams,
    |              ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^
...
87  |     Terminal, TerminalReader, 
TerminalReaderCreateParams, TerminalReaderListParams,
    |     ^^^^^^^^                  
^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^
88  |     TerminalLocation, TerminalLocationCreateParams, 
TerminalConnectionToken,
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
91  |     RadarEarlyFraudWarning, RadarValueList, 
RadarValueListCreateParams, RadarValueListItem,
    |     ^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^
...
94  |     ReportRun, ReportRunCreateParams, ReportType, 
ReportTypeListParams,
    |     ^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^
...
97  |     BillingPortalSession, BillingPortalConfiguration, 
Invoice, InvoiceCreateParams,
    |                                                       
         ^^^^^^^^^^^^^^^^^^^
98  |     InvoiceItem, InvoiceItemCreateParams, 
SubscriptionSchedule, SubscriptionScheduleCreateParams,
    |                  ^^^^^^^^^^^^^^^^^^^^^^^              
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
99  |     UsageRecord, UsageRecordCreateParams, Plan, 
PlanCreateParams,
    |                  ^^^^^^^^^^^^^^^^^^^^^^^        
^^^^^^^^^^^^^^^^
...
102 |     Tax, TaxCalculation, TaxCalculationCreateParams, 
TaxTransaction, TaxTransactionCreateParams,
    |     ^^^                  ^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
103 |     TaxRate, TaxRateCreateParams, TaxCode, 
TaxCodeListParams,
    |              ^^^^^^^^^^^^^^^^^^^           
^^^^^^^^^^^^^^^^^
...
106 |     Identity, IdentityVerificationSession, 
IdentityVerificationSessionCreateParams,
    |     ^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
107 |     IdentityVerificationReport, 
IdentityVerificationReportListParams,
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
110 |     Climate, ClimateOrder, ClimateOrderCreateParams, 
ClimateProduct, ClimateSupplier,
    |     ^^^^^^^  ^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^
...
113 |     FinancialConnectionsAccount, 
FinancialConnectionsAccountListParams,
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
114 |     FinancialConnectionsSession, 
FinancialConnectionsSessionCreateParams,
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
117 |     SigmaScheduledQueryRun, 
SigmaScheduledQueryRunListParams,
    |     ^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
120 |     SetupIntent, SetupIntentCreateParams, 
SetupIntentConfirmParams,
    |                  ^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^
...
123 |     Source, SourceCreateParams, SourceUpdateParams,
    |             ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^
...
126 |     Token, TokenCreateParams,
    |            ^^^^^^^^^^^^^^^^^
...
129 |     Coupon, CouponCreateParams, PromotionCode, 
PromotionCodeCreateParams,
    |             ^^^^^^^^^^^^^^^^^^                 
^^^^^^^^^^^^^^^^^^^^^^^^^
...
132 |     Review, ReviewListParams, ReviewApproveParams,
    |             ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^
...
135 |     Topup, TopupCreateParams, TopupListParams,
    |            ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^
...
138 |     Mandate, MandateRetrieveParams,
    |              ^^^^^^^^^^^^^^^^^^^^^
...
141 |     PaymentLink, PaymentLinkCreateParams, 
PaymentLinkUpdateParams,
    |                  ^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^
...
144 |     CheckoutSession, CheckoutSessionCreateParams, 
CheckoutSessionListParams,
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^
...
147 |     Quote, QuoteCreateParams, QuoteListParams, 
QuoteFinalizeParams,
    |            ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^
...
150 |     TestClock, TestClockCreateParams, 
TestClockAdvanceParams,
    |     ^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^
...
153 |     AppsSecret, AppsSecretCreateParams,
    |     ^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: consider importing one of these variants 
instead:
            
stripe::TaxProductResourceJurisdictionLevel::Country
            stripe::TaxRateJurisdictionLevel::Country
            stripe::generated::tax_calculation::tax_calculat
ion_line_item::TaxProductResourceJurisdictionLevel::Country
    = help: consider importing one of these enums instead:
            crate::models::card::CardStatus
            crate::models::credit_card::CardStatus
help: a similar name exists in the module
    |
32  -     Card, CardCreateParams, CardUpdateParams, 
CardType, CardStatus, CardShipping,
32  +     Card, CardCreateParams, CardUpdateParams, 
CardType, ChargeStatus, CardShipping,
    |
help: a similar name exists in the module
    |
32  -     Card, CardCreateParams, CardUpdateParams, 
CardType, CardStatus, CardShipping,
32  +     Card, CardCreateParams, CardUpdateParams, 
CardType, CardStatus, Shipping,
    |

error[E0432]: unresolved imports `sysinfo::SystemExt`, 
`sysinfo::ProcessExt`
    --> src\services\stripe.rs:2229:31
     |
2229 |         use sysinfo::{System, SystemExt, ProcessExt};
     |                               ^^^^^^^^^  ^^^^^^^^^^ 
no `ProcessExt` in the root
     |                               |
     |                               no `SystemExt` in the 
root
     |
help: a similar name exists in the module
     |
2229 -         use sysinfo::{System, SystemExt, ProcessExt};
2229 +         use sysinfo::{System, System, ProcessExt};
     |
help: a similar name exists in the module
     |
2229 -         use sysinfo::{System, SystemExt, ProcessExt};
2229 +         use sysinfo::{System, SystemExt, Process};
     |

error[E0432]: unresolved imports `sysinfo::SystemExt`, 
`sysinfo::ProcessExt`
    --> src\services\stripe.rs:2243:31
     |
2243 |         use sysinfo::{System, SystemExt, ProcessExt};
     |                               ^^^^^^^^^  ^^^^^^^^^^ 
no `ProcessExt` in the root
     |                               |
     |                               no `SystemExt` in the 
root
     |
help: a similar name exists in the module
     |
2243 -         use sysinfo::{System, SystemExt, ProcessExt};
2243 +         use sysinfo::{System, System, ProcessExt};
     |
help: a similar name exists in the module
     |
2243 -         use sysinfo::{System, SystemExt, ProcessExt};
2243 +         use sysinfo::{System, SystemExt, Process};
     |

error[E0432]: unresolved import `backoff::future`
   --> src\services\transaction.rs:6:35
    |
6   | use backoff::{ExponentialBackoff, future::retry};
    |                                   ^^^^^^ could not 
find `future` in `backoff`
    |
note: found an item that was configured out
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:223:9
    |
223 | pub mod future;
    |         ^^^^^^
note: the item is gated behind the `futures` feature
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:221:7
    |
221 | #[cfg(feature = "futures")]
    |       ^^^^^^^^^^^^^^^^^^^

error[E0432]: unresolved import `backoff::future`
   --> src\services\uploader.rs:9:35
    |
9   | use backoff::{ExponentialBackoff, future::retry};
    |                                   ^^^^^^ could not 
find `future` in `backoff`
    |
note: found an item that was configured out
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:223:9
    |
223 | pub mod future;
    |         ^^^^^^
note: the item is gated behind the `futures` feature
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:221:7
    |
221 | #[cfg(feature = "futures")]
    |       ^^^^^^^^^^^^^^^^^^^

error[E0432]: unresolved import `backoff::future`
   --> src\services\webhook.rs:6:35
    |
6   | use backoff::{ExponentialBackoff, future::retry};
    |                                   ^^^^^^ could not 
find `future` in `backoff`
    |
note: found an item that was configured out
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:223:9
    |
223 | pub mod future;
    |         ^^^^^^
note: the item is gated behind the `futures` feature
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\lib.rs:221:7
    |
221 | #[cfg(feature = "futures")]
    |       ^^^^^^^^^^^^^^^^^^^

error[E0432]: unresolved import 
`crate::models::oauth::OAuthCode`
  --> src\controllers\oauth_controller.rs:12:41
   |
12 | use crate::models::oauth::{OAuthClient, OAuthCode, 
OAuthToken, TokenBlacklist};
   |                                         ^^^^^^^^^
   |                                         |
   |                                         no `OAuthCode` 
in `models::oauth`
   |                                         help: a 
similar name exists in the module: `OAuthToken`

error: cannot find macro `json` in this scope
  --> src\routes\health.rs:88:29
   |
88 |     HttpResponse::Ok().json(json!({
   |                             ^^^^
   |
help: consider importing one of these macros
   |
1  + use crate::routes::json;
   |
1  + use serde_json::json;
   |

error: cannot find macro `json` in this scope
  --> src\routes\health.rs:71:33
   |
71 |         HttpResponse::Ok().json(json!({
   |                                 ^^^^
   |
help: consider importing one of these macros
   |
1  + use crate::routes::json;
   |
1  + use serde_json::json;
   |

error: cannot find macro `json` in this scope
  --> src\routes\health.rs:62:33
   |
62 |         HttpResponse::Ok().json(json!({
   |                                 ^^^^
   |
help: consider importing one of these macros
   |
1  + use crate::routes::json;
   |
1  + use serde_json::json;
   |

error: cannot find macro `json` in this scope
  --> src\routes\health.rs:46:49
   |
46 |         HttpResponse::ServiceUnavailable().json(json!({
   |                                                 ^^^^
   |
help: consider importing one of these macros
   |
1  + use crate::routes::json;
   |
1  + use serde_json::json;
   |

error: cannot find macro `json` in this scope
  --> src\routes\health.rs:37:33
   |
37 |         HttpResponse::Ok().json(json!({
   |                                 ^^^^
   |
help: consider importing one of these macros
   |
1  + use crate::routes::json;
   |
1  + use serde_json::json;
   |

error: cannot find macro `json` in this scope
  --> src\routes\health.rs:16:29
   |
16 |     HttpResponse::Ok().json(json!({
   |                             ^^^^
   |
help: consider importing one of these macros
   |
1  + use crate::routes::json;
   |
1  + use serde_json::json;
   |

error[E0433]: failed to resolve: could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`
   --> src\models\card.rs:343:60
    |
343 | ...                   "per_authorization" => stripe::C
ardSpendingControlsSpendingLimitInterval::PerAuthorization,
    |                                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`

error[E0433]: failed to resolve: could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`
   --> src\models\card.rs:344:48
    |
344 | ...                   "daily" => 
stripe::CardSpendingControlsSpendingLimitInterval::Daily,
    |                                          
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`

error[E0433]: failed to resolve: could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`
   --> src\models\card.rs:345:49
    |
345 | ...                   "weekly" => 
stripe::CardSpendingControlsSpendingLimitInterval::Weekly,
    |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`

error[E0433]: failed to resolve: could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`
   --> src\models\card.rs:346:50
    |
346 | ...                   "monthly" => 
stripe::CardSpendingControlsSpendingLimitInterval::Monthly,
    |                                            
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`

error[E0433]: failed to resolve: could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`
   --> src\models\card.rs:347:49
    |
347 | ...                   "yearly" => 
stripe::CardSpendingControlsSpendingLimitInterval::Yearly,
    |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`

error[E0433]: failed to resolve: could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`
   --> src\models\card.rs:348:51
    |
348 | ...                   "all_time" => 
stripe::CardSpendingControlsSpendingLimitInterval::AllTime,
    |                                             
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`

error[E0433]: failed to resolve: could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`
   --> src\models\card.rs:349:42
    |
349 | ...                   _ => 
stripe::CardSpendingControlsSpendingLimitInterval::Daily,
    |                                    
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`CardSpendingControlsSpendingLimitInterval` in `stripe`

error[E0433]: failed to resolve: could not find 
`TerminalConnectionTokenCreateParams` in `stripe`
    --> src\services\stripe.rs:1491:38
     |
1491 |             let mut params = 
stripe::TerminalConnectionTokenCreateParams::default();
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TerminalConnectionTokenCreateParams` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsAddressSource` in `stripe`
    --> src\services\stripe.rs:1545:42
     |
1545 |                     "billing" => 
stripe::TaxCalculationCustomerDetailsAddressSource::Billing,
     |                                          
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                          |
     |                                          could not 
find `TaxCalculationCustomerDetailsAddressSource` in 
`stripe`
     |                                          help: an 
enum with a similar name exists: 
`TaxProductResourceCustomerDetailsAddressSource`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsAddressSource` in `stripe`
    --> src\services\stripe.rs:1546:43
     |
1546 |                     "shipping" => stripe::TaxCalculat
ionCustomerDetailsAddressSource::Shipping,
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                           |
     |                                           could not 
find `TaxCalculationCustomerDetailsAddressSource` in 
`stripe`
     |                                           help: an 
enum with a similar name exists: 
`TaxProductResourceCustomerDetailsAddressSource`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsAddressSource` in `stripe`
    --> src\services\stripe.rs:1547:34
     |
1547 |                     _ => 
stripe::TaxCalculationCustomerDetailsAddressSource::Billing,
     |                                  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                  |
     |                                  could not find 
`TaxCalculationCustomerDetailsAddressSource` in `stripe`
     |                                  help: an enum with 
a similar name exists: 
`TaxProductResourceCustomerDetailsAddressSource`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`
    --> src\services\stripe.rs:1551:39
     |
1551 |                     "none" => 
stripe::TaxCalculationCustomerDetailsTaxExempt::None,
     |                                       
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`
    --> src\services\stripe.rs:1552:41
     |
1552 |                     "exempt" => 
stripe::TaxCalculationCustomerDetailsTaxExempt::Exempt,
     |                                         
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`
    --> src\services\stripe.rs:1553:42
     |
1553 |                     "reverse" => 
stripe::TaxCalculationCustomerDetailsTaxExempt::Reverse,
     |                                          
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`
    --> src\services\stripe.rs:1554:34
     |
1554 |                     _ => 
stripe::TaxCalculationCustomerDetailsTaxExempt::None,
     |                                  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
    --> src\services\stripe.rs:1557:39
     |
1557 |                     "none" => stripe::TaxCalculationC
ustomerDetailsTaxabilityOverride::None,
     |                                       
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                       |
     |                                       could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
     |                                       help: an enum 
with a similar name exists: 
`TaxProductResourceCustomerDetailsTaxabilityOverride`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
    --> src\services\stripe.rs:1558:50
     |
1558 |                     "customer_exempt" => stripe::TaxC
alculationCustomerDetailsTaxabilityOverride::CustomerExempt,
     |                                                  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                                  |
     |                                                  
could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
     |                                                  
help: an enum with a similar name exists: 
`TaxProductResourceCustomerDetailsTaxabilityOverride`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
    --> src\services\stripe.rs:1559:49
     |
1559 |                     "reverse_charge" => stripe::TaxCa
lculationCustomerDetailsTaxabilityOverride::ReverseCharge,
     |                                                 
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                                 |
     |                                                 
could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
     |                                                 
help: an enum with a similar name exists: 
`TaxProductResourceCustomerDetailsTaxabilityOverride`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
    --> src\services\stripe.rs:1560:34
     |
1560 |                     _ => stripe::TaxCalculationCustom
erDetailsTaxabilityOverride::None,
     |                                  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                  |
     |                                  could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
     |                                  help: an enum with 
a similar name exists: 
`TaxProductResourceCustomerDetailsTaxabilityOverride`

error[E0433]: failed to resolve: could not find 
`TaxCalculationShippingCostTaxBehavior` in `stripe`
    --> src\services\stripe.rs:1567:48
     |
1567 |                     tax_behavior: Some(stripe::TaxCal
culationShippingCostTaxBehavior::Exclusive),
     |                                                
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                                |
     |                                                could 
not find `TaxCalculationShippingCostTaxBehavior` in `stripe`
     |                                                help: 
an enum with a similar name exists: 
`TaxCalculationLineItemTaxBehavior`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsAddressSource` in `stripe`
    --> src\services\stripe.rs:1589:46
     |
1589 |                         "billing" => 
stripe::TaxCalculationCustomerDetailsAddressSource::Billing,
     |                                              
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                              |
     |                                              could 
not find `TaxCalculationCustomerDetailsAddressSource` in 
`stripe`
     |                                              help: 
an enum with a similar name exists: 
`TaxProductResourceCustomerDetailsAddressSource`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsAddressSource` in `stripe`
    --> src\services\stripe.rs:1590:47
     |
1590 |                         "shipping" => stripe::TaxCalc
ulationCustomerDetailsAddressSource::Shipping,
     |                                               
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                               |
     |                                               could 
not find `TaxCalculationCustomerDetailsAddressSource` in 
`stripe`
     |                                               help: 
an enum with a similar name exists: 
`TaxProductResourceCustomerDetailsAddressSource`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsAddressSource` in `stripe`
    --> src\services\stripe.rs:1591:38
     |
1591 |                         _ => 
stripe::TaxCalculationCustomerDetailsAddressSource::Billing,
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                      |
     |                                      could not find 
`TaxCalculationCustomerDetailsAddressSource` in `stripe`
     |                                      help: an enum 
with a similar name exists: 
`TaxProductResourceCustomerDetailsAddressSource`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`
    --> src\services\stripe.rs:1595:43
     |
1595 |                         "none" => 
stripe::TaxCalculationCustomerDetailsTaxExempt::None,
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`
    --> src\services\stripe.rs:1596:45
     |
1596 |                         "exempt" => 
stripe::TaxCalculationCustomerDetailsTaxExempt::Exempt,
     |                                             
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`
    --> src\services\stripe.rs:1597:46
     |
1597 |                         "reverse" => 
stripe::TaxCalculationCustomerDetailsTaxExempt::Reverse,
     |                                              
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`
    --> src\services\stripe.rs:1598:38
     |
1598 |                         _ => 
stripe::TaxCalculationCustomerDetailsTaxExempt::None,
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`TaxCalculationCustomerDetailsTaxExempt` in `stripe`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
    --> src\services\stripe.rs:1601:43
     |
1601 |                         "none" => stripe::TaxCalculat
ionCustomerDetailsTaxabilityOverride::None,
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                           |
     |                                           could not 
find `TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
     |                                           help: an 
enum with a similar name exists: 
`TaxProductResourceCustomerDetailsTaxabilityOverride`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
    --> src\services\stripe.rs:1602:54
     |
1602 |                         "customer_exempt" => stripe::
TaxCalculationCustomerDetailsTaxabilityOverride::CustomerExe
mpt,
     |                                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                                      
|
     |                                                      
could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
     |                                                      
help: an enum with a similar name exists: 
`TaxProductResourceCustomerDetailsTaxabilityOverride`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
    --> src\services\stripe.rs:1603:53
     |
1603 |                         "reverse_charge" => stripe::T
axCalculationCustomerDetailsTaxabilityOverride::ReverseCharg
e,
     |                                                     
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                                     |
     |                                                     
could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
     |                                                     
help: an enum with a similar name exists: 
`TaxProductResourceCustomerDetailsTaxabilityOverride`

error[E0433]: failed to resolve: could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
    --> src\services\stripe.rs:1604:38
     |
1604 |                         _ => stripe::TaxCalculationCu
stomerDetailsTaxabilityOverride::None,
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                      |
     |                                      could not find 
`TaxCalculationCustomerDetailsTaxabilityOverride` in 
`stripe`
     |                                      help: an enum 
with a similar name exists: 
`TaxProductResourceCustomerDetailsTaxabilityOverride`

error[E0433]: failed to resolve: could not find 
`TaxTransactionLineItemTaxBehavior` in `stripe`
    --> src\services\stripe.rs:1636:48
     |
1636 |                         "inclusive" => 
stripe::TaxTransactionLineItemTaxBehavior::Inclusive,
     |                                                
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                                |
     |                                                could 
not find `TaxTransactionLineItemTaxBehavior` in `stripe`
     |                                                help: 
an enum with a similar name exists: 
`TaxCalculationLineItemTaxBehavior`

error[E0433]: failed to resolve: could not find 
`TaxTransactionLineItemTaxBehavior` in `stripe`
    --> src\services\stripe.rs:1637:48
     |
1637 |                         "exclusive" => 
stripe::TaxTransactionLineItemTaxBehavior::Exclusive,
     |                                                
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                                |
     |                                                could 
not find `TaxTransactionLineItemTaxBehavior` in `stripe`
     |                                                help: 
an enum with a similar name exists: 
`TaxCalculationLineItemTaxBehavior`

error[E0433]: failed to resolve: could not find 
`TaxTransactionLineItemTaxBehavior` in `stripe`
    --> src\services\stripe.rs:1638:38
     |
1638 |                         _ => 
stripe::TaxTransactionLineItemTaxBehavior::Exclusive,
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                      |
     |                                      could not find 
`TaxTransactionLineItemTaxBehavior` in `stripe`
     |                                      help: an enum 
with a similar name exists: 
`TaxCalculationLineItemTaxBehavior`

error[E0433]: failed to resolve: could not find 
`IdentityVerificationSessionType` in `stripe`
    --> src\services\stripe.rs:1680:39
     |
1680 |                 "document" => 
stripe::IdentityVerificationSessionType::Document,
     |                                       
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`IdentityVerificationSessionType` in `stripe`

error[E0433]: failed to resolve: could not find 
`IdentityVerificationSessionType` in `stripe`
    --> src\services\stripe.rs:1681:40
     |
1681 |                 "id_number" => 
stripe::IdentityVerificationSessionType::IdNumber,
     |                                        
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`IdentityVerificationSessionType` in `stripe`

error[E0433]: failed to resolve: could not find 
`IdentityVerificationSessionType` in `stripe`
    --> src\services\stripe.rs:1682:30
     |
1682 |                 _ => 
stripe::IdentityVerificationSessionType::Document,
     |                              
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`IdentityVerificationSessionType` in `stripe`

error[E0433]: failed to resolve: could not find 
`ClimateProductListParams` in `stripe`
    --> src\services\stripe.rs:1755:38
     |
1755 |             let mut params = 
stripe::ClimateProductListParams::default();
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`ClimateProductListParams` in `stripe`

error[E0433]: failed to resolve: could not find 
`ClimateSupplierListParams` in `stripe`
    --> src\services\stripe.rs:1768:38
     |
1768 |             let mut params = 
stripe::ClimateSupplierListParams::default();
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`ClimateSupplierListParams` in `stripe`

error[E0433]: failed to resolve: could not find 
`FinancialConnectionsSessionAccountHolderType` in `stripe`
    --> src\services\stripe.rs:1790:43
     |
1790 |                     "consumer" => stripe::FinancialCo
nnectionsSessionAccountHolderType::Consumer,
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`FinancialConnectionsSessionAccountHolderType` in `stripe`

error[E0433]: failed to resolve: could not find 
`FinancialConnectionsSessionAccountHolderType` in `stripe`
    --> src\services\stripe.rs:1791:43
     |
1791 |                     "business" => stripe::FinancialCo
nnectionsSessionAccountHolderType::Business,
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`FinancialConnectionsSessionAccountHolderType` in `stripe`

error[E0433]: failed to resolve: could not find 
`FinancialConnectionsSessionAccountHolderType` in `stripe`
    --> src\services\stripe.rs:1792:34
     |
1792 |                     _ => stripe::FinancialConnections
SessionAccountHolderType::Consumer,
     |                                  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`FinancialConnectionsSessionAccountHolderType` in `stripe`

error[E0433]: failed to resolve: could not find 
`FinancialConnectionsSessionPermissions` in `stripe`
    --> src\services\stripe.rs:1799:45
     |
1799 |                 "payment_method" => stripe::Financial
ConnectionsSessionPermissions::PaymentMethod,
     |                                             
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`FinancialConnectionsSessionPermissions` in `stripe`

error[E0433]: failed to resolve: could not find 
`FinancialConnectionsSessionPermissions` in `stripe`
    --> src\services\stripe.rs:1800:39
     |
1800 |                 "balances" => 
stripe::FinancialConnectionsSessionPermissions::Balances,
     |                                       
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`FinancialConnectionsSessionPermissions` in `stripe`

error[E0433]: failed to resolve: could not find 
`FinancialConnectionsSessionPermissions` in `stripe`
    --> src\services\stripe.rs:1801:40
     |
1801 |                 "ownership" => 
stripe::FinancialConnectionsSessionPermissions::Ownership,
     |                                        
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`FinancialConnectionsSessionPermissions` in `stripe`

error[E0433]: failed to resolve: could not find 
`FinancialConnectionsSessionPermissions` in `stripe`
    --> src\services\stripe.rs:1802:43
     |
1802 |                 "transactions" => stripe::FinancialCo
nnectionsSessionPermissions::Transactions,
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`FinancialConnectionsSessionPermissions` in `stripe`

error[E0433]: failed to resolve: could not find 
`FinancialConnectionsSessionPermissions` in `stripe`
    --> src\services\stripe.rs:1803:30
     |
1803 |                 _ => stripe::FinancialConnectionsSess
ionPermissions::PaymentMethod,
     |                              
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`FinancialConnectionsSessionPermissions` in `stripe`

error[E0433]: failed to resolve: could not find 
`BillingPortalSessionCreateParams` in `stripe`
    --> src\services\stripe.rs:1903:38
     |
1903 |             let mut params = 
stripe::BillingPortalSessionCreateParams::default();
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                      |
     |                                      could not find 
`BillingPortalSessionCreateParams` in `stripe`
     |                                      help: an enum 
with a similar name exists: `BillingPortalSessionLocale`

error[E0433]: failed to resolve: could not find 
`CreatePaymentLinkBillingAddressCollection` in `stripe`
    --> src\services\stripe.rs:2056:39
     |
2056 |                     "auto" => 
stripe::CreatePaymentLinkBillingAddressCollection::Auto,
     |                                       
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                       |
     |                                       could not find 
`CreatePaymentLinkBillingAddressCollection` in `stripe`
     |                                       help: a struct 
with a similar name exists: 
`CreatePaymentLinkShippingAddressCollection`

error[E0433]: failed to resolve: could not find 
`CreatePaymentLinkBillingAddressCollection` in `stripe`
    --> src\services\stripe.rs:2057:43
     |
2057 |                     "required" => 
stripe::CreatePaymentLinkBillingAddressCollection::Required,
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                           |
     |                                           could not 
find `CreatePaymentLinkBillingAddressCollection` in `stripe`
     |                                           help: a 
struct with a similar name exists: 
`CreatePaymentLinkShippingAddressCollection`

error[E0433]: failed to resolve: could not find 
`CreatePaymentLinkBillingAddressCollection` in `stripe`
    --> src\services\stripe.rs:2058:34
     |
2058 |                     _ => 
stripe::CreatePaymentLinkBillingAddressCollection::Auto,
     |                                  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                  |
     |                                  could not find 
`CreatePaymentLinkBillingAddressCollection` in `stripe`
     |                                  help: a struct with 
a similar name exists: 
`CreatePaymentLinkShippingAddressCollection`

error[E0433]: failed to resolve: could not find 
`CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
    --> src\services\stripe.rs:2094:50
     |
2094 | ...                   "day" => stripe::CreateSubscrip
tionItemsPriceDataRecurringInterval::Day,
     |                                        
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                        |
     |                                        could not 
find `CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
     |                                        help: an enum 
with a similar name exists: `CreateSubscriptionSchedulePhase
sItemsPriceDataRecurringInterval`

error[E0433]: failed to resolve: could not find 
`CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
    --> src\services\stripe.rs:2095:51
     |
2095 | ...                   "week" => stripe::CreateSubscri
ptionItemsPriceDataRecurringInterval::Week,
     |                                         
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                         |
     |                                         could not 
find `CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
     |                                         help: an 
enum with a similar name exists: `CreateSubscriptionSchedule
PhasesItemsPriceDataRecurringInterval`

error[E0433]: failed to resolve: could not find 
`CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
    --> src\services\stripe.rs:2096:52
     |
2096 | ...                   "month" => stripe::CreateSubscr
iptionItemsPriceDataRecurringInterval::Month,
     |                                          
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                          |
     |                                          could not 
find `CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
     |                                          help: an 
enum with a similar name exists: `CreateSubscriptionSchedule
PhasesItemsPriceDataRecurringInterval`

error[E0433]: failed to resolve: could not find 
`CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
    --> src\services\stripe.rs:2097:51
     |
2097 | ...                   "year" => stripe::CreateSubscri
ptionItemsPriceDataRecurringInterval::Year,
     |                                         
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                         |
     |                                         could not 
find `CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
     |                                         help: an 
enum with a similar name exists: `CreateSubscriptionSchedule
PhasesItemsPriceDataRecurringInterval`

error[E0433]: failed to resolve: could not find 
`CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
    --> src\services\stripe.rs:2098:46
     |
2098 | ...                   _ => stripe::CreateSubscription
ItemsPriceDataRecurringInterval::Month,
     |                                    
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                    |
     |                                    could not find 
`CreateSubscriptionItemsPriceDataRecurringInterval` in 
`stripe`
     |                                    help: an enum 
with a similar name exists: `CreateSubscriptionSchedulePhase
sItemsPriceDataRecurringInterval`

error[E0433]: failed to resolve: could not find 
`SetupIntentUsage` in `stripe`
    --> src\services\stripe.rs:2142:45
     |
2142 |                     "on_session" => 
stripe::SetupIntentUsage::OnSession,
     |                                             
^^^^^^^^^^^^^^^^
     |                                             |
     |                                             could 
not find `SetupIntentUsage` in `stripe`
     |                                             help: a 
struct with a similar name exists: `SetupIntent`

error[E0433]: failed to resolve: could not find 
`SetupIntentUsage` in `stripe`
    --> src\services\stripe.rs:2143:46
     |
2143 |                     "off_session" => 
stripe::SetupIntentUsage::OffSession,
     |                                              
^^^^^^^^^^^^^^^^
     |                                              |
     |                                              could 
not find `SetupIntentUsage` in `stripe`
     |                                              help: a 
struct with a similar name exists: `SetupIntent`

error[E0433]: failed to resolve: could not find 
`SetupIntentUsage` in `stripe`
    --> src\services\stripe.rs:2144:34
     |
2144 |                     _ => 
stripe::SetupIntentUsage::OffSession,
     |                                  ^^^^^^^^^^^^^^^^
     |                                  |
     |                                  could not find 
`SetupIntentUsage` in `stripe`
     |                                  help: a struct with 
a similar name exists: `SetupIntent`

error[E0433]: failed to resolve: could not find 
`ApplicationFeeRefundCreateParams` in `stripe`
    --> src\services\stripe.rs:2168:38
     |
2168 |             let mut params = 
stripe::ApplicationFeeRefundCreateParams::default();
     |                                      
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ could not find 
`ApplicationFeeRefundCreateParams` in `stripe`

error[E0433]: failed to resolve: could not find 
`CardReplacementReason` in `stripe`
    --> src\services\stripe.rs:2490:38
     |
2490 |                 "damaged" => 
stripe::CardReplacementReason::Damaged,
     |                                      
^^^^^^^^^^^^^^^^^^^^^
     |                                      |
     |                                      could not find 
`CardReplacementReason` in `stripe`
     |                                      help: an enum 
with a similar name exists: `IssuingCardReplacementReason`

error[E0433]: failed to resolve: could not find 
`CardReplacementReason` in `stripe`
    --> src\services\stripe.rs:2491:38
     |
2491 |                 "expired" => 
stripe::CardReplacementReason::Expired,
     |                                      
^^^^^^^^^^^^^^^^^^^^^
     |                                      |
     |                                      could not find 
`CardReplacementReason` in `stripe`
     |                                      help: an enum 
with a similar name exists: `IssuingCardReplacementReason`

error[E0433]: failed to resolve: could not find 
`CardReplacementReason` in `stripe`
    --> src\services\stripe.rs:2492:35
     |
2492 |                 "lost" => 
stripe::CardReplacementReason::Lost,
     |                                   
^^^^^^^^^^^^^^^^^^^^^
     |                                   |
     |                                   could not find 
`CardReplacementReason` in `stripe`
     |                                   help: an enum with 
a similar name exists: `IssuingCardReplacementReason`

error[E0433]: failed to resolve: could not find 
`CardReplacementReason` in `stripe`
    --> src\services\stripe.rs:2493:37
     |
2493 |                 "stolen" => 
stripe::CardReplacementReason::Stolen,
     |                                     
^^^^^^^^^^^^^^^^^^^^^
     |                                     |
     |                                     could not find 
`CardReplacementReason` in `stripe`
     |                                     help: an enum 
with a similar name exists: `IssuingCardReplacementReason`

error[E0433]: failed to resolve: could not find 
`CardReplacementReason` in `stripe`
    --> src\services\stripe.rs:2494:30
     |
2494 |                 _ => 
stripe::CardReplacementReason::Damaged,
     |                              ^^^^^^^^^^^^^^^^^^^^^
     |                              |
     |                              could not find 
`CardReplacementReason` in `stripe`
     |                              help: an enum with a 
similar name exists: `IssuingCardReplacementReason`

error[E0433]: failed to resolve: could not find 
`RetrieveCard` in `stripe`
    --> src\services\stripe.rs:2516:34
     |
2516 |         let mut params = 
stripe::RetrieveCard::default();
     |                                  ^^^^^^^^^^^^ could 
not find `RetrieveCard` in `stripe`

error[E0433]: failed to resolve: could not find 
`PaymentIntentCaptureParams` in `stripe`
    --> src\services\stripe.rs:3005:34
     |
3005 |         let mut params = 
stripe::PaymentIntentCaptureParams::default();
     |                                  
^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                  |
     |                                  could not find 
`PaymentIntentCaptureParams` in `stripe`
     |                                  help: a struct with 
a similar name exists: `PaymentIntentConfirmParams`

error[E0433]: failed to resolve: could not find 
`PaymentIntentCancelParams` in `stripe`
    --> src\services\stripe.rs:3022:34
     |
3022 |         let mut params = 
stripe::PaymentIntentCancelParams::default();
     |                                  
^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                  |
     |                                  could not find 
`PaymentIntentCancelParams` in `stripe`
     |                                  help: a struct with 
a similar name exists: `PaymentIntentConfirmParams`

error[E0433]: failed to resolve: could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
    --> src\services\stripe.rs:3193:39
     |
3193 |                 "canceled" => 
stripe::CreateIssuingDisputeEvidenceReason::Canceled,
     |                                       
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                       |
     |                                       could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
     |                                       help: an enum 
with a similar name exists: `IssuingDisputeEvidenceReason`

error[E0433]: failed to resolve: could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
    --> src\services\stripe.rs:3194:40
     |
3194 |                 "duplicate" => 
stripe::CreateIssuingDisputeEvidenceReason::Duplicate,
     |                                        
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                        |
     |                                        could not 
find `CreateIssuingDisputeEvidenceReason` in `stripe`
     |                                        help: an enum 
with a similar name exists: `IssuingDisputeEvidenceReason`

error[E0433]: failed to resolve: could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
    --> src\services\stripe.rs:3195:41
     |
3195 |                 "fraudulent" => 
stripe::CreateIssuingDisputeEvidenceReason::Fraudulent,
     |                                         
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                         |
     |                                         could not 
find `CreateIssuingDisputeEvidenceReason` in `stripe`
     |                                         help: an 
enum with a similar name exists: 
`IssuingDisputeEvidenceReason`

error[E0433]: failed to resolve: could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
    --> src\services\stripe.rs:3196:59
     |
3196 |                 "merchandise_not_as_described" => str
ipe::CreateIssuingDisputeEvidenceReason::MerchandiseNotAsDes
cribed,
     |                                                      
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                                      
     |
     |                                                      
     could not find `CreateIssuingDisputeEvidenceReason` in 
`stripe`
     |                                                      
     help: an enum with a similar name exists: 
`IssuingDisputeEvidenceReason`

error[E0433]: failed to resolve: could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
    --> src\services\stripe.rs:3197:43
     |
3197 |                 "not_received" => 
stripe::CreateIssuingDisputeEvidenceReason::NotReceived,
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                           |
     |                                           could not 
find `CreateIssuingDisputeEvidenceReason` in `stripe`
     |                                           help: an 
enum with a similar name exists: 
`IssuingDisputeEvidenceReason`

error[E0433]: failed to resolve: could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
    --> src\services\stripe.rs:3198:36
     |
3198 |                 "other" => 
stripe::CreateIssuingDisputeEvidenceReason::Other,
     |                                    
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                                    |
     |                                    could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
     |                                    help: an enum 
with a similar name exists: `IssuingDisputeEvidenceReason`

error[E0433]: failed to resolve: could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
    --> src\services\stripe.rs:3199:30
     |
3199 |                 _ => 
stripe::CreateIssuingDisputeEvidenceReason::Other,
     |                              
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |                              |
     |                              could not find 
`CreateIssuingDisputeEvidenceReason` in `stripe`
     |                              help: an enum with a 
similar name exists: `IssuingDisputeEvidenceReason`

error[E0422]: cannot find struct, variant or union type 
`Balance` in module `crate::models::account`
   --> src\controllers\developer_api_controller.rs:386:58
    |
386 |             
account.balance.push(crate::models::account::Balance {
    |                                                       
   ^^^^^^^ not found in `crate::models::account`
    |
help: consider importing one of these items
    |
1   + use stripe::Balance;
    |
1   + use stripe::EventObject::Balance;
    |
1   + use tower::balance::p2c::Balance;
    |
help: if you import `Balance`, refer to it directly
    |
386 -             
account.balance.push(crate::models::account::Balance {
386 +             account.balance.push(Balance {
    |

error[E0422]: cannot find struct, variant or union type 
`TransactionParty` in module `crate::models::transaction`
   --> src\controllers\developer_api_controller.rs:430:57
    |
430 |             recipient: 
Some(crate::models::transaction::TransactionParty {
    |                                                       
  ^^^^^^^^^^^^^^^^ help: a struct with a similar name 
exists: `Transaction`
    |
   ::: src\models\transaction.rs:100:1
    |
100 | pub struct Transaction {
    | ---------------------- similarly named struct 
`Transaction` defined here

error[E0531]: cannot find tuple struct or tuple variant 
`RawBody` in module `axum::extract`
   --> src\controllers\stripe.rs:264:24
    |
264 |         axum::extract::RawBody(body): 
axum::extract::RawBody,
    |                        ^^^^^^^ not found in 
`axum::extract`

error[E0412]: cannot find type `RawBody` in module 
`axum::extract`
   --> src\controllers\stripe.rs:264:54
    |
264 |         axum::extract::RawBody(body): 
axum::extract::RawBody,
    |                                                      
^^^^^^^ not found in `axum::extract`

error[E0433]: failed to resolve: could not find `stripe` in 
`models`
   --> src\controllers\stripe.rs:330:48
    |
330 |     pub billing_details: 
Option<crate::models::stripe::BillingDetails>,
    |                                                ^^^^^^ 
could not find `stripe` in `models`
    |
help: consider importing this module
    |
1   + use crate::services::stripe;
    |
help: if you import `stripe`, refer to it directly
    |
330 -     pub billing_details: 
Option<crate::models::stripe::BillingDetails>,
330 +     pub billing_details: 
Option<stripe::BillingDetails>,
    |

error[E0531]: cannot find tuple struct or tuple variant 
`RawBody` in module `axum::extract`
   --> src\controllers\withdrawal.rs:165:24
    |
165 |         axum::extract::RawBody(body): 
axum::extract::RawBody,
    |                        ^^^^^^^ not found in 
`axum::extract`

error[E0412]: cannot find type `RawBody` in module 
`axum::extract`
   --> src\controllers\withdrawal.rs:165:54
    |
165 |         axum::extract::RawBody(body): 
axum::extract::RawBody,
    |                                                      
^^^^^^^ not found in `axum::extract`

error[E0412]: cannot find type `CardSpendingControls` in 
crate `stripe`
   --> src\models\card.rs:177:43
    |
177 |     pub spending_controls: 
Option<stripe::CardSpendingControls>,
    |                                           
^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0412]: cannot find type `CardShipping` in crate 
`stripe`
   --> src\models\card.rs:179:42
    |
179 |     pub shipping_address: 
Option<stripe::CardShipping>,
    |                                          ^^^^^^^^^^^^ 
help: a struct with a similar name exists: `Shipping`
    |
   ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\sh
ipping.rs:11:1
    |
11  | pub struct Shipping {
    | ------------------- similarly named struct `Shipping` 
defined here

error[E0412]: cannot find type `CardSpendingControls` in 
crate `stripe`
   --> src\models\card.rs:333:35
    |
333 |     pub fn build(self) -> 
stripe::CardSpendingControls {
    |                                   
^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`CardSpendingControls` in crate `stripe`
   --> src\models\card.rs:334:17
    |
334 |         stripe::CardSpendingControls {
    |                 ^^^^^^^^^^^^^^^^^^^^ not found in 
`stripe`

error[E0422]: cannot find struct, variant or union type 
`CardSpendingControlsSpendingLimit` in crate `stripe`
   --> src\models\card.rs:339:29
    |
339 |                     
stripe::CardSpendingControlsSpendingLimit {
    |                             
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0405]: cannot find trait `Responder` in this scope
  --> src\routes\health.rs:15:33
   |
15 | async fn health_check() -> impl Responder {
   |                                 ^^^^^^^^^ not found in 
this scope
   |
help: consider importing one of these traits
   |
1  + use crate::routes::Responder;
   |
1  + use actix_web::Responder;
   |

error[E0433]: failed to resolve: use of undeclared type 
`HttpResponse`
  --> src\routes\health.rs:16:5
   |
16 |     HttpResponse::Ok().json(json!({
   |     ^^^^^^^^^^^^ use of undeclared type `HttpResponse`
   |
help: consider importing one of these structs
   |
1  + use crate::routes::HttpResponse;
   |
1  + use actix_web::HttpResponse;
   |

error[E0405]: cannot find trait `Responder` in this scope
  --> src\routes\health.rs:28:25
   |
28 | async fn ping() -> impl Responder {
   |                         ^^^^^^^^^ not found in this 
scope
   |
help: consider importing one of these traits
   |
1  + use crate::routes::Responder;
   |
1  + use actix_web::Responder;
   |

error[E0433]: failed to resolve: use of undeclared type 
`HttpResponse`
  --> src\routes\health.rs:29:5
   |
29 |     HttpResponse::Ok().body("pong")
   |     ^^^^^^^^^^^^ use of undeclared type `HttpResponse`
   |
help: consider importing one of these structs
   |
1  + use crate::routes::HttpResponse;
   |
1  + use actix_web::HttpResponse;
   |

error[E0412]: cannot find type `AppState` in this scope
  --> src\routes\health.rs:33:42
   |
33 | async fn database_health(data: web::Data<AppState>) -> 
impl Responder {
   |                                          ^^^^^^^^ not 
found in this scope
   |
help: consider importing this struct
   |
1  + use crate::AppState;
   |

error[E0405]: cannot find trait `Responder` in this scope
  --> src\routes\health.rs:33:61
   |
33 | async fn database_health(data: web::Data<AppState>) -> 
impl Responder {
   |                                                        
     ^^^^^^^^^ not found in this scope
   |
help: consider importing one of these traits
   |
1  + use crate::routes::Responder;
   |
1  + use actix_web::Responder;
   |

error[E0433]: failed to resolve: use of undeclared type 
`HttpResponse`
  --> src\routes\health.rs:37:9
   |
37 |         HttpResponse::Ok().json(json!({
   |         ^^^^^^^^^^^^ use of undeclared type 
`HttpResponse`
   |
help: consider importing one of these structs
   |
1  + use crate::routes::HttpResponse;
   |
1  + use actix_web::HttpResponse;
   |

error[E0433]: failed to resolve: use of undeclared type 
`HttpResponse`
  --> src\routes\health.rs:46:9
   |
46 |         HttpResponse::ServiceUnavailable().json(json!({
   |         ^^^^^^^^^^^^ use of undeclared type 
`HttpResponse`
   |
help: consider importing one of these structs
   |
1  + use crate::routes::HttpResponse;
   |
1  + use actix_web::HttpResponse;
   |

error[E0412]: cannot find type `AppState` in this scope
  --> src\routes\health.rs:58:39
   |
58 | async fn cache_health(data: web::Data<AppState>) -> 
impl Responder {
   |                                       ^^^^^^^^ not 
found in this scope
   |
help: consider importing this struct
   |
1  + use crate::AppState;
   |

error[E0405]: cannot find trait `Responder` in this scope
  --> src\routes\health.rs:58:58
   |
58 | async fn cache_health(data: web::Data<AppState>) -> 
impl Responder {
   |                                                        
  ^^^^^^^^^ not found in this scope
   |
help: consider importing one of these traits
   |
1  + use crate::routes::Responder;
   |
1  + use actix_web::Responder;
   |

error[E0433]: failed to resolve: use of undeclared type 
`HttpResponse`
  --> src\routes\health.rs:62:9
   |
62 |         HttpResponse::Ok().json(json!({
   |         ^^^^^^^^^^^^ use of undeclared type 
`HttpResponse`
   |
help: consider importing one of these structs
   |
1  + use crate::routes::HttpResponse;
   |
1  + use actix_web::HttpResponse;
   |

error[E0433]: failed to resolve: use of undeclared type 
`HttpResponse`
  --> src\routes\health.rs:71:9
   |
71 |         HttpResponse::Ok().json(json!({
   |         ^^^^^^^^^^^^ use of undeclared type 
`HttpResponse`
   |
help: consider importing one of these structs
   |
1  + use crate::routes::HttpResponse;
   |
1  + use actix_web::HttpResponse;
   |

error[E0412]: cannot find type `AppState` in this scope
  --> src\routes\health.rs:84:42
   |
84 | async fn detailed_health(data: web::Data<AppState>) -> 
impl Responder {
   |                                          ^^^^^^^^ not 
found in this scope
   |
help: consider importing this struct
   |
1  + use crate::AppState;
   |

error[E0405]: cannot find trait `Responder` in this scope
  --> src\routes\health.rs:84:61
   |
84 | async fn detailed_health(data: web::Data<AppState>) -> 
impl Responder {
   |                                                        
     ^^^^^^^^^ not found in this scope
   |
help: consider importing one of these traits
   |
1  + use crate::routes::Responder;
   |
1  + use actix_web::Responder;
   |

error[E0433]: failed to resolve: use of undeclared type 
`HttpResponse`
  --> src\routes\health.rs:88:5
   |
88 |     HttpResponse::Ok().json(json!({
   |     ^^^^^^^^^^^^ use of undeclared type `HttpResponse`
   |
help: consider importing one of these structs
   |
1  + use crate::routes::HttpResponse;
   |
1  + use actix_web::HttpResponse;
   |

error[E0422]: cannot find struct, variant or union type 
`Balance` in module `crate::models::account`
   --> src\services\account_number.rs:131:37
    |
131 |             crate::models::account::Balance {
    |                                     ^^^^^^^ not found 
in `crate::models::account`
    |
help: consider importing one of these items
    |
1   + use stripe::Balance;
    |
1   + use stripe::EventObject::Balance;
    |
1   + use tower::balance::p2c::Balance;
    |
help: if you import `Balance`, refer to it directly
    |
131 -             crate::models::account::Balance {
131 +             Balance {
    |

error[E0422]: cannot find struct, variant or union type 
`Balance` in module `crate::models::account`
   --> src\services\account_number.rs:137:37
    |
137 |             crate::models::account::Balance {
    |                                     ^^^^^^^ not found 
in `crate::models::account`
    |
help: consider importing one of these items
    |
1   + use stripe::Balance;
    |
1   + use stripe::EventObject::Balance;
    |
1   + use tower::balance::p2c::Balance;
    |
help: if you import `Balance`, refer to it directly
    |
137 -             crate::models::account::Balance {
137 +             Balance {
    |

error[E0422]: cannot find struct, variant or union type 
`Balance` in module `crate::models::account`
   --> src\services\account_number.rs:143:37
    |
143 |             crate::models::account::Balance {
    |                                     ^^^^^^^ not found 
in `crate::models::account`
    |
help: consider importing one of these items
    |
1   + use stripe::Balance;
    |
1   + use stripe::EventObject::Balance;
    |
1   + use tower::balance::p2c::Balance;
    |
help: if you import `Balance`, refer to it directly
    |
143 -             crate::models::account::Balance {
143 +             Balance {
    |

error[E0422]: cannot find struct, variant or union type 
`Balance` in module `crate::models::account`
   --> src\services\account_number.rs:149:37
    |
149 |             crate::models::account::Balance {
    |                                     ^^^^^^^ not found 
in `crate::models::account`
    |
help: consider importing one of these items
    |
1   + use stripe::Balance;
    |
1   + use stripe::EventObject::Balance;
    |
1   + use tower::balance::p2c::Balance;
    |
help: if you import `Balance`, refer to it directly
    |
149 -             crate::models::account::Balance {
149 +             Balance {
    |

error[E0422]: cannot find struct, variant or union type 
`TaxCalculationCustomerDetails` in crate `stripe`
    --> src\services\stripe.rs:1535:47
     |
1535 |             params.customer_details = 
stripe::TaxCalculationCustomerDetails {
     |                                               
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`TaxCalculationShippingCost` in crate `stripe`
    --> src\services\stripe.rs:1565:53
     |
1565 |                 params.shipping_cost = 
Some(stripe::TaxCalculationShippingCost {
     |                                                     
^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`TaxCalculationCustomerDetails` in crate `stripe`
    --> src\services\stripe.rs:1579:43
     |
1579 |                 customer_details: 
stripe::TaxCalculationCustomerDetails {
     |                                           
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`TaxTransactionLineItem` in crate `stripe`
    --> src\services\stripe.rs:1632:25
     |
1632 |                 stripe::TaxTransactionLineItem {
     |                         ^^^^^^^^^^^^^^^^^^^^^^ help: 
a struct with a similar name exists: 
`TaxCalculationLineItem`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\t
ax_calculation_line_item.rs:12:1
     |
12   | pub struct TaxCalculationLineItem {
     | --------------------------------- similarly named 
struct `TaxCalculationLineItem` defined here

error[E0422]: cannot find struct, variant or union type 
`FinancialConnectionsSessionAccountHolder` in crate `stripe`
    --> src\services\stripe.rs:1788:45
     |
1788 |             params.account_holder = 
stripe::FinancialConnectionsSessionAccountHolder {
     |                                             
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in 
`stripe`

error[E0422]: cannot find struct, variant or union type 
`CreateSubscriptionItemsPriceData` in crate `stripe`
    --> src\services\stripe.rs:2087:66
     |
2087 |                     price_data: 
item.price_data.map(|pd| 
stripe::CreateSubscriptionItemsPriceData {
     |                                                      
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: a struct 
with a similar name exists: `SubscriptionItemPriceData`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\s
ubscription_item.rs:359:1
     |
359  | pub struct SubscriptionItemPriceData {
     | ------------------------------------ similarly named 
struct `SubscriptionItemPriceData` defined here

error[E0422]: cannot find struct, variant or union type 
`CreateSubscriptionItemsPriceDataRecurring` in crate 
`stripe`
    --> src\services\stripe.rs:2092:44
     |
2092 |                         recurring: 
stripe::CreateSubscriptionItemsPriceDataRecurring {
     |                                            
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: a struct 
with a similar name exists: 
`SubscriptionItemPriceDataRecurring`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\s
ubscription_item.rs:391:1
     |
391  | pub struct SubscriptionItemPriceDataRecurring {
     | --------------------------------------------- 
similarly named struct `SubscriptionItemPriceDataRecurring` 
defined here

error[E0422]: cannot find struct, variant or union type 
`CardholderBilling` in crate `stripe`
    --> src\services\stripe.rs:2290:39
     |
2290 |         params.billing = 
Some(stripe::CardholderBilling {
     |                                       
^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`CardholderIndividual` in crate `stripe`
    --> src\services\stripe.rs:2303:46
     |
2303 |             params.individual = 
Some(stripe::CardholderIndividual {
     |                                              
^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`CardholderIndividualDob` in crate `stripe`
    --> src\services\stripe.rs:2306:35
     |
2306 |                 dob: 
Some(stripe::CardholderIndividualDob {
     |                                   
^^^^^^^^^^^^^^^^^^^^^^^ help: a struct with a similar name 
exists: `IssuingCardholderIndividualDob`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\i
ssuing_cardholder.rs:152:1
     |
152  | pub struct IssuingCardholderIndividualDob {
     | ----------------------------------------- similarly 
named struct `IssuingCardholderIndividualDob` defined here

error[E0422]: cannot find struct, variant or union type 
`CardholderIndividualCardIssuing` in crate `stripe`
    --> src\services\stripe.rs:2311:72
     |
2311 |                 card_issuing: 
individual.card_issuing.map(|ci| 
stripe::CardholderIndividualCardIssuing {
     |                                                      
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found 
in `stripe`

error[E0422]: cannot find struct, variant or union type 
`CardholderIndividualCardIssuingUserTermsAcceptance` in 
crate `stripe`
    --> src\services\stripe.rs:2313:33
     |
2313 |                         
stripe::CardholderIndividualCardIssuingUserTermsAcceptance {
     |                                 
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not 
found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`AccountTosAcceptance` in crate `stripe`
    --> src\services\stripe.rs:2349:46
     |
2349 |         params.tos_acceptance = 
Some(stripe::AccountTosAcceptance {
     |                                              
^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`PersonDob` in crate `stripe`
    --> src\services\stripe.rs:2360:35
     |
2360 |                 dob: Some(stripe::PersonDob {
     |                                   ^^^^^^^^^ help: a 
struct with a similar name exists: `Person`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\p
erson.rs:15:1
     |
15   | pub struct Person {
     | ----------------- similarly named struct `Person` 
defined here

error[E0422]: cannot find struct, variant or union type 
`AccountCapabilitiesCardPayments` in crate `stripe`
    --> src\services\stripe.rs:2371:41
     |
2371 |             card_payments: 
Some(stripe::AccountCapabilitiesCardPayments {
     |                                         
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: a struct with a 
similar name exists: `CreateAccountCapabilitiesCardPayments`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\a
ccount.rs:2000:1
     |
2000 | pub struct CreateAccountCapabilitiesCardPayments {
     | ------------------------------------------------ 
similarly named struct 
`CreateAccountCapabilitiesCardPayments` defined here

error[E0422]: cannot find struct, variant or union type 
`AccountCapabilitiesTransfers` in crate `stripe`
    --> src\services\stripe.rs:2374:37
     |
2374 |             transfers: 
Some(stripe::AccountCapabilitiesTransfers {
     |                                     
^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: a struct with a similar 
name exists: `CreateAccountCapabilitiesTransfers`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\a
ccount.rs:2240:1
     |
2240 | pub struct CreateAccountCapabilitiesTransfers {
     | --------------------------------------------- 
similarly named struct `CreateAccountCapabilitiesTransfers` 
defined here

error[E0422]: cannot find struct, variant or union type 
`ExternalAccountCreateParams` in crate `stripe`
    --> src\services\stripe.rs:2388:30
     |
2388 |         let params = 
stripe::ExternalAccountCreateParams {
     |                              
^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0412]: cannot find type `CardShipping` in crate 
`stripe`
    --> src\services\stripe.rs:2441:42
     |
2441 |         shipping_address: 
Option<stripe::CardShipping>,
     |                                          
^^^^^^^^^^^^ help: a struct with a similar name exists: 
`Shipping`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\s
hipping.rs:11:1
     |
11   | pub struct Shipping {
     | ------------------- similarly named struct 
`Shipping` defined here

error[E0412]: cannot find type `CardSpendingControls` in 
crate `stripe`
    --> src\services\stripe.rs:2442:43
     |
2442 |         spending_controls: 
Option<stripe::CardSpendingControls>,
     |                                           
^^^^^^^^^^^^^^^^^^^^ not found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`CardPin` in crate `stripe`
    --> src\services\stripe.rs:2463:39
     |
2463 |             params.pin = Some(stripe::CardPin {
     |                                       ^^^^^^^ not 
found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`CreateIssuingDisputeEvidence` in crate `stripe`
    --> src\services\stripe.rs:3144:40
     |
3144 |         params.evidence = 
Some(stripe::CreateIssuingDisputeEvidence {
     |                                        
^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: a struct with a similar 
name exists: `IssuingDisputeEvidence`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\i
ssuing_dispute.rs:69:1
     |
69   | pub struct IssuingDisputeEvidence {
     | --------------------------------- similarly named 
struct `IssuingDisputeEvidence` defined here

error[E0422]: cannot find struct, variant or union type 
`CreateIssuingDisputeEvidenceCanceled` in crate `stripe`
    --> src\services\stripe.rs:3145:57
     |
3145 |             canceled: evidence.canceled.map(|c| 
stripe::CreateIssuingDisputeEvidenceCanceled {
     |                                                      
   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in 
`stripe`

error[E0422]: cannot find struct, variant or union type 
`CreateIssuingDisputeEvidenceDuplicate` in crate `stripe`
    --> src\services\stripe.rs:3157:59
     |
3157 |             duplicate: evidence.duplicate.map(|d| 
stripe::CreateIssuingDisputeEvidenceDuplicate {
     |                                                      
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in 
`stripe`

error[E0422]: cannot find struct, variant or union type 
`CreateIssuingDisputeEvidenceFraudulent` in crate `stripe`
    --> src\services\stripe.rs:3165:61
     |
3165 |             fraudulent: evidence.fraudulent.map(|f| 
stripe::CreateIssuingDisputeEvidenceFraudulent {
     |                                                      
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in 
`stripe`

error[E0422]: cannot find struct, variant or union type 
`CreateIssuingDisputeEvidenceMerchandiseNotAsDescribed` in 
crate `stripe`
    --> src\services\stripe.rs:3170:25
     |
3170 |                 stripe::CreateIssuingDisputeEvidenceM
erchandiseNotAsDescribed {
     |                         
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not 
found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`CreateIssuingDisputeEvidenceNotReceived` in crate `stripe`
    --> src\services\stripe.rs:3179:65
     |
3179 |             not_received: 
evidence.not_received.map(|n| 
stripe::CreateIssuingDisputeEvidenceNotReceived {
     |                                                      
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not 
found in `stripe`

error[E0422]: cannot find struct, variant or union type 
`CreateIssuingDisputeEvidenceOther` in crate `stripe`
    --> src\services\stripe.rs:3186:51
     |
3186 |             other: evidence.other.map(|o| 
stripe::CreateIssuingDisputeEvidenceOther {
     |                                                   
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: a struct with a 
similar name exists: `IssuingDisputeEvidence`
     |
    ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\async-stripe-0.34.1\src\resources\generated\i
ssuing_dispute.rs:69:1
     |
69   | pub struct IssuingDisputeEvidence {
     | --------------------------------- similarly named 
struct `IssuingDisputeEvidence` defined here

error[E0422]: cannot find struct, variant or union type 
`CreateIssuingDisputeEvidenceServiceNotAsDescribed` in 
crate `stripe`
    --> src\services\stripe.rs:3202:25
     |
3202 |                 
stripe::CreateIssuingDisputeEvidenceServiceNotAsDescribed {
     |                         
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found 
in `stripe`

error[E0423]: cannot initialize a tuple struct which 
contains private fields
   --> src\utils\date_utils.rs:89:9
    |
89  |     
Err(chrono::ParseError(chrono::ParseErrorKind::Invalid))
    |         ^^^^^^^^^^^^^^^^^^
    |
note: constructor is not visible here due to private fields
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\chrono-0.4.40\src\format\mod.rs:389:23
    |
389 | pub struct ParseError(ParseErrorKind);
    |                       ^^^^^^^^^^^^^^ private field
help: consider importing one of these items instead
    |
1   + use actix_web::error::ContentTypeError::ParseError;
    |
1   + use actix_web::error::UrlGenerationError::ParseError;
    |
1   + use rustls::CertRevocationListError::ParseError;
    |
help: if you import `ParseError`, refer to it directly
    |
89  -     
Err(chrono::ParseError(chrono::ParseErrorKind::Invalid))
89  +     Err(ParseError(chrono::ParseErrorKind::Invalid))
    |

error[E0433]: failed to resolve: could not find 
`ParseErrorKind` in `chrono`
  --> src\utils\date_utils.rs:89:36
   |
89 |     
Err(chrono::ParseError(chrono::ParseErrorKind::Invalid))
   |                                    ^^^^^^^^^^^^^^ 
could not find `ParseErrorKind` in `chrono`
   |
help: a struct with a similar name exists
   |
89 -     
Err(chrono::ParseError(chrono::ParseErrorKind::Invalid))
89 +     
Err(chrono::ParseError(chrono::ParseError::Invalid))
   |
help: consider importing one of these enums
   |
1  + use 
crate::utils::date_utils::chrono::format::ParseErrorKind;
   |
1  + use chrono::format::ParseErrorKind;
   |
help: if you import `ParseErrorKind`, refer to it directly
   |
89 -     
Err(chrono::ParseError(chrono::ParseErrorKind::Invalid))
89 +     Err(chrono::ParseError(ParseErrorKind::Invalid))
   |

error[E0603]: struct import `Database` is private
   --> src\controllers\admin_controller.rs:13:22
    |
13  | use crate::database::Database;
    |                      ^^^^^^^^ private struct import
    |
note: the struct import `Database` is defined here...
   --> src\database.rs:2:23
    |
2   | use mongodb::{Client, Database, Collection};
    |                       ^^^^^^^^
note: ...and refers to the struct `Database` which is 
defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\mongodb-2.8.2\src\lib.rs:365:5
    |
365 |     db::Database,
    |     ^^^^^^^^^^^^ you could import this directly
help: import `Database` directly
    |
13  - use crate::database::Database;
13  + use mongodb::db::Database;
    |

error[E0603]: struct import `Database` is private
   --> src\controllers\developer_api.rs:14:22
    |
14  | use crate::database::Database;
    |                      ^^^^^^^^ private struct import
    |
note: the struct import `Database` is defined here...
   --> src\database.rs:2:23
    |
2   | use mongodb::{Client, Database, Collection};
    |                       ^^^^^^^^
note: ...and refers to the struct `Database` which is 
defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\mongodb-2.8.2\src\lib.rs:365:5
    |
365 |     db::Database,
    |     ^^^^^^^^^^^^ you could import this directly
help: import `Database` directly
    |
14  - use crate::database::Database;
14  + use mongodb::db::Database;
    |

error[E0603]: struct import `Database` is private
   --> src\controllers\health.rs:11:22
    |
11  | use crate::database::Database;
    |                      ^^^^^^^^ private struct import
    |
note: the struct import `Database` is defined here...
   --> src\database.rs:2:23
    |
2   | use mongodb::{Client, Database, Collection};
    |                       ^^^^^^^^
note: ...and refers to the struct `Database` which is 
defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\mongodb-2.8.2\src\lib.rs:365:5
    |
365 |     db::Database,
    |     ^^^^^^^^^^^^ you could import this directly
help: import `Database` directly
    |
11  - use crate::database::Database;
11  + use mongodb::db::Database;
    |

error[E0603]: struct import `Database` is private
   --> src\controllers\withdrawal.rs:14:22
    |
14  | use crate::database::Database;
    |                      ^^^^^^^^ private struct import
    |
note: the struct import `Database` is defined here...
   --> src\database.rs:2:23
    |
2   | use mongodb::{Client, Database, Collection};
    |                       ^^^^^^^^
note: ...and refers to the struct `Database` which is 
defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\mongodb-2.8.2\src\lib.rs:365:5
    |
365 |     db::Database,
    |     ^^^^^^^^^^^^ you could import this directly
help: import `Database` directly
    |
14  - use crate::database::Database;
14  + use mongodb::db::Database;
    |

error[E0603]: struct import `Database` is private
   --> src\services\withdrawal.rs:11:22
    |
11  | use crate::database::Database;
    |                      ^^^^^^^^ private struct import
    |
note: the struct import `Database` is defined here...
   --> src\database.rs:2:23
    |
2   | use mongodb::{Client, Database, Collection};
    |                       ^^^^^^^^
note: ...and refers to the struct `Database` which is 
defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\mongodb-2.8.2\src\lib.rs:365:5
    |
365 |     db::Database,
    |     ^^^^^^^^^^^^ you could import this directly
help: import `Database` directly
    |
11  - use crate::database::Database;
11  + use mongodb::db::Database;
    |

warning: unused import: `middleware::Logger`
 --> src\main.rs:1:39
  |
1 | use actix_web::{App, HttpServer, web, 
middleware::Logger};
  |                                       ^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `debug`
 --> src\main.rs:3:24
  |
3 | use log::{info, error, debug};
  |                        ^^^^^

warning: unused import: `mongodb::bson::oid::ObjectId`
 --> src\main.rs:6:5
  |
6 | use mongodb::bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `tower::ServiceBuilder`
 --> src\main.rs:8:5
  |
8 | use tower::ServiceBuilder;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `limit::RequestBodyLimitLayer`, 
`timeout::TimeoutLayer`, and `trace::TraceLayer`
  --> src\main.rs:10:5
   |
10 |     trace::TraceLayer,
   |     ^^^^^^^^^^^^^^^^^
11 |     timeout::TimeoutLayer,
   |     ^^^^^^^^^^^^^^^^^^^^^
12 |     limit::RequestBodyLimitLayer,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `mongodb::bson::oid::ObjectId`
 --> src\controllers\admin_controller.rs:9:5
  |
9 | use mongodb::bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Serialize`
  --> src\controllers\admin_controller.rs:10:26
   |
10 | use serde::{Deserialize, Serialize};
   |                          ^^^^^^^^^

warning: unused import: `crate::models::user::User`
  --> src\controllers\admin_controller.rs:14:5
   |
14 | use crate::models::user::User;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `warn`
 --> src\controllers\auth_controller.rs:2:24
  |
2 | use log::{error, info, warn};
  |                        ^^^^

warning: unused import: `oid::ObjectId`
 --> src\controllers\auth_controller.rs:3:26
  |
3 | use mongodb::bson::{doc, oid::ObjectId};
  |                          ^^^^^^^^^^^^^

warning: unused import: `hash_password`
  --> src\controllers\auth_controller.rs:13:63
   |
13 | use crate::utils::security::{sanitize_input, 
verify_password, hash_password};
   |                                                        
       ^^^^^^^^^^^^^

warning: unused import: `Path`
 --> src\controllers\developer_api.rs:4:15
  |
4 |     extract::{Path, Query, State},
  |               ^^^^

warning: unused imports: `Payout`, `User`, and 
`WebhookCallbackResponse`
  --> src\controllers\developer_api.rs:17:14
   |
17 |     payout::{Payout, PayoutRequest, PayoutResponse},
   |              ^^^^^^
18 |     user::{User, UserDetails},
   |            ^^^^
19 |     webhook::{WebhookConfig, WebhookEvent, 
WebhookCallbackResponse},
   |                                            
^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `warn`
 --> src\controllers\developer_api_controller.rs:2:24
  |
2 | use log::{error, info, warn};
  |                        ^^^^

warning: unused import: `oid::ObjectId`
 --> src\controllers\developer_api_controller.rs:3:26
  |
3 | use mongodb::bson::{doc, oid::ObjectId};
  |                          ^^^^^^^^^^^^^

warning: unused import: `tower::ServiceBuilder`
 --> src\controllers\developer_api_controller.rs:9:5
  |
9 | use tower::ServiceBuilder;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::time::Duration`
  --> src\controllers\developer_api_controller.rs:10:5
   |
10 | use std::time::Duration;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused imports: `FailedWebhook`, `WebhookConfig`, 
and `WebhookDeliveryLog`
  --> src\controllers\developer_api_controller.rs:16:30
   |
16 | use crate::models::webhook::{WebhookConfig, 
FailedWebhook, WebhookDeliveryLog};
   |                              ^^^^^^^^^^^^^  
^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^

warning: unused import: 
`crate::models::credit_card::CreditCard`
  --> src\controllers\developer_api_controller.rs:17:5
   |
17 | use crate::models::credit_card::CreditCard;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src\controllers\exchange_controller.rs:1:5
  |
1 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `extract::State`
 --> src\controllers\exchange_controller.rs:4:5
  |
4 |     extract::State,
  |     ^^^^^^^^^^^^^^

warning: unused import: `Deserialize`
 --> src\controllers\exchange_controller.rs:8:13
  |
8 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^

warning: unused import: `serde_json::json`
 --> src\controllers\exchange_controller.rs:9:5
  |
9 | use serde_json::json;
  |     ^^^^^^^^^^^^^^^^

warning: unused import: `messages`
  --> src\controllers\exchange_controller.rs:11:46
   |
11 | use crate::utils::response::{ResponseHelper, messages};
   |                                              ^^^^^^^^

warning: unused import: `Deserialize`
 --> src\controllers\health.rs:8:13
  |
8 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^

warning: unused imports: `ResponseHelper`, `messages`, and 
`suggestions`
  --> src\controllers\health.rs:12:30
   |
12 | use crate::utils::response::{ResponseHelper, messages, 
suggestions};
   |                              ^^^^^^^^^^^^^^  ^^^^^^^^  
^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src\controllers\helper_controller.rs:1:5
  |
1 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `extract::State`
 --> src\controllers\helper_controller.rs:4:5
  |
4 |     extract::State,
  |     ^^^^^^^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\controllers\helper_controller.rs:8:13
  |
8 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `warn`
 --> src\controllers\oauth_controller.rs:2:24
  |
2 | use log::{error, info, warn};
  |                        ^^^^

warning: unused import: `std::collections::HashMap`
 --> src\controllers\oauth_controller.rs:9:5
  |
9 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `TokenBlacklist`
  --> src\controllers\oauth_controller.rs:12:64
   |
12 | use crate::models::oauth::{OAuthClient, OAuthCode, 
OAuthToken, TokenBlacklist};
   |                                                        
        ^^^^^^^^^^^^^^

warning: unused import: `DecodedToken`
  --> src\controllers\oauth_controller.rs:14:44
   |
14 | use crate::services::token::{TokenService, 
DecodedToken};
   |                                            ^^^^^^^^^^^^

warning: unused import: `mongodb::bson::oid::ObjectId`
 --> src\controllers\stripe.rs:9:5
  |
9 | use mongodb::bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Serialize`
  --> src\controllers\stripe.rs:10:26
   |
10 | use serde::{Deserialize, Serialize};
   |                          ^^^^^^^^^

warning: unused import: `ConnectedAccountDetails`
  --> src\controllers\stripe.rs:16:25
   |
16 |     user::{UserDetails, ConnectedAccountDetails},
   |                         ^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `warn`
 --> src\controllers\user_controller.rs:2:24
  |
2 | use log::{error, info, warn};
  |                        ^^^^

warning: unused import: `tower::ServiceBuilder`
 --> src\controllers\user_controller.rs:9:5
  |
9 | use tower::ServiceBuilder;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::time::Duration`
  --> src\controllers\user_controller.rs:10:5
   |
10 | use std::time::Duration;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `verify_password`
  --> src\controllers\user_controller.rs:16:61
   |
16 | use crate::utils::security::{sanitize_input, 
hash_password, verify_password};
   |                                                        
     ^^^^^^^^^^^^^^^

warning: unused import: `Serialize`
  --> src\controllers\withdrawal.rs:10:26
   |
10 | use serde::{Deserialize, Serialize};
   |                          ^^^^^^^^^

warning: unused import: `PayoutStatus`
  --> src\controllers\withdrawal.rs:15:60
   |
15 | use crate::models::payout::{PayoutRequest, 
PayoutResponse, PayoutStatus};
   |                                                        
    ^^^^^^^^^^^^

warning: unused import: `error`
 --> src\database.rs:4:17
  |
4 | use log::{info, error};
  |                 ^^^^^

warning: unused import: `HttpMessage`
 --> src\middleware\security.rs:3:12
  |
3 |     Error, HttpMessage,
  |            ^^^^^^^^^^^

warning: unused import: `actix_cors::Cors`
 --> src\middleware\security.rs:5:5
  |
5 | use actix_cors::Cors;
  |     ^^^^^^^^^^^^^^^^

warning: unused imports: `error`, `info`, and `warn`
 --> src\middleware\security.rs:9:11
  |
9 | use log::{info, warn, error};
  |           ^^^^  ^^^^  ^^^^^

warning: unused import: `std::future::Future`
  --> src\middleware\security.rs:10:5
   |
10 | use std::future::Future;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::pin::Pin`
  --> src\middleware\security.rs:11:5
   |
11 | use std::pin::Pin;
   |     ^^^^^^^^^^^^^

warning: unused imports: `Context` and `Poll`
  --> src\middleware\security.rs:12:17
   |
12 | use std::task::{Context, Poll};
   |                 ^^^^^^^  ^^^^

warning: unused import: `std::time::Duration`
  --> src\middleware\security.rs:15:5
   |
15 | use std::time::Duration;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `Error`
  --> src\middleware\security.rs:16:35
   |
16 | use actix_web::{HttpRequest, web, Error};
   |                                   ^^^^^

warning: unused import: `Clock`
  --> src\middleware\security.rs:18:37
   |
18 | use governor::clock::{DefaultClock, Clock};
   |                                     ^^^^^

warning: unused imports: `ServiceRequest`, 
`ServiceResponse`, `Service`, `Transform`, and 
`forward_ready`
 --> src\middleware\auth.rs:2:11
  |
2 |     dev::{forward_ready, Service, ServiceRequest, 
ServiceResponse, Transform},
  |           ^^^^^^^^^^^^^  ^^^^^^^  ^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^  ^^^^^^^^^

warning: unused imports: `LocalBoxFuture`, `Ready`, and 
`ready`
 --> src\middleware\auth.rs:5:23
  |
5 | use futures::future::{ready, LocalBoxFuture, Ready, 
Future};
  |                       ^^^^^  ^^^^^^^^^^^^^^  ^^^^^

warning: unused imports: `Error as DeError` and `self`
 --> src\models\account.rs:5:17
  |
5 | use serde::de::{self, Error as DeError};
  |                 ^^^^  ^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `self`
 --> src\models\oauth.rs:2:21
  |
2 | use mongodb::bson::{self, oid::ObjectId, DateTime};
  |                     ^^^^                 ^^^^^^^^

warning: unused imports: `HttpRequest` and `HttpResponse`
 --> src\routes\account.rs:1:22
  |
1 | use actix_web::{web, HttpResponse, HttpRequest};
  |                      ^^^^^^^^^^^^  ^^^^^^^^^^^

warning: unused import: `serde_json::json`
 --> src\routes\account.rs:2:5
  |
2 | use serde_json::json;
  |     ^^^^^^^^^^^^^^^^

warning: unused import: `AuthenticatedUser`
 --> src\routes\account.rs:8:31
  |
8 | use crate::middleware::auth::{AuthenticatedUser, 
auth_middleware};
  |                               ^^^^^^^^^^^^^^^^^

warning: unused import: `auth_middleware`
 --> src\routes\admin.rs:4:31
  |
4 | use crate::middleware::auth::{auth_middleware, 
admin_middleware};
  |                               ^^^^^^^^^^^^^^^

warning: unused import: `Scope`
 --> src\routes\auth.rs:1:22
  |
1 | use actix_web::{web, Scope};
  |                      ^^^^^

warning: unused import: `Scope`
 --> src\routes\oauth.rs:1:22
  |
1 | use actix_web::{web, Scope};
  |                      ^^^^^

warning: unused import: `info`
 --> src\services\audit_log.rs:2:11
  |
2 | use log::{info, warn, error};
  |           ^^^^

warning: unused import: `info`
 --> src\services\auth.rs:7:18
  |
7 | use log::{error, info};
  |                  ^^^^

warning: unused imports: `error` and `warn`
 --> src\services\exchange.rs:8:11
  |
8 | use log::{error, info, warn};
  |           ^^^^^        ^^^^

warning: unused import: `std::collections::HashMap`
 --> src\services\id.rs:1:5
  |
1 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src\services\id.rs:3:5
  |
3 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src\services\oauth.rs:1:5
  |
1 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Duration`
 --> src\services\oauth.rs:3:17
  |
3 | use std::time::{Duration, SystemTime, UNIX_EPOCH};
  |                 ^^^^^^^^

warning: unused import: `Context`
 --> src\services\oauth.rs:5:14
  |
5 | use anyhow::{Context, Result, anyhow};
  |              ^^^^^^^

warning: unused import: `DateTime`
 --> src\services\oauth.rs:7:19
  |
7 | use chrono::{Utc, DateTime};
  |                   ^^^^^^^^

warning: unused import: `error`
 --> src\services\oauth.rs:8:11
  |
8 | use log::{error, info, warn};
  |           ^^^^^

warning: unused import: `std::collections::HashMap`
 --> src\services\payout.rs:1:5
  |
1 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Context`
 --> src\services\payout.rs:4:14
  |
4 | use anyhow::{Context, Result, anyhow};
  |              ^^^^^^^

warning: unused import: `warn`
 --> src\services\payout.rs:7:24
  |
7 | use log::{error, info, warn};
  |                        ^^^^

warning: unused import: `arc_swap::ArcSwap`
 --> src\services\stripe.rs:7:5
  |
7 | use arc_swap::ArcSwap;
  |     ^^^^^^^^^^^^^^^^^

warning: unused imports: `error` and `warn`
  --> src\services\stripe.rs:11:11
   |
11 | use log::{error, info, warn};
   |           ^^^^^        ^^^^

warning: unused import: `RwLock as TokioRwLock`
  --> src\services\stripe.rs:17:30
   |
17 | use tokio::sync::{Semaphore, RwLock as TokioRwLock};
   |                              ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `Level` and `span`
  --> src\services\stripe.rs:20:27
   |
20 | use tracing::{instrument, span, Level};
   |                           ^^^^  ^^^^^

warning: unused imports: `AccountCapabilities`, 
`AccountRequirements`, `ApplicationFee`, 
`BillingPortalConfiguration`, `Coupon`, `Expandable`, 
`InvoiceItem`, `Invoice`, `Mandate`, `Metadata`, 
`PaymentIntentCaptureMethod`, `PaymentIntentConfirmParams`, 
`PaymentIntentConfirmationMethod`, `PaymentIntentStatus`, 
`PaymentIntentUpdateParams`, `Plan`, `Price`, `Product`, 
`PromotionCode`, `Quote`, `RadarValueListItem`, `Review`, 
`Source`, `SubscriptionItem`, `SubscriptionSchedule`, 
`TaxRate`, `Token`, `Topup`, `TransferReversal`, 
`UsageRecord`, and `WebhookEndpoint`
   --> src\services\stripe.rs:25:23
    |
25  |     Client, Currency, Expandable, List, 
ListPagination, Metadata,
    |                       ^^^^^^^^^^                      
  ^^^^^^^^
...
29  |     AccountCapabilities, AccountSettings, 
AccountRequirements, AccountPayouts,
    |     ^^^^^^^^^^^^^^^^^^^                   
^^^^^^^^^^^^^^^^^^^
...
46  |     PaymentIntent, PaymentIntentCreateParams, 
PaymentIntentUpdateParams, PaymentIntentConfirmParams,
    |                                               
^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
47  |     PaymentIntentStatus, PaymentIntentCaptureMethod, 
PaymentIntentConfirmationMethod,
    |     ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
57  |     Transfer, TransferCreateParams, 
TransferListParams, TransferReversal,
    |                                                       
  ^^^^^^^^^^^^^^^^
...
68  |     Event, EventListParams, EventType, EventObject, 
Webhook, WebhookEndpoint,
    |                                                       
       ^^^^^^^^^^^^^^^
...
74  |     Product, ProductCreateParams, Price, 
PriceCreateParams,
    |     ^^^^^^^                       ^^^^^
75  |     Subscription, SubscriptionCreateParams, 
SubscriptionItem,
    |                                             
^^^^^^^^^^^^^^^^
...
84  |     ApplicationFee, ApplicationFeeRefund,
    |     ^^^^^^^^^^^^^^
...
91  |     RadarEarlyFraudWarning, RadarValueList, 
RadarValueListCreateParams, RadarValueListItem,
    |                                                       
                  ^^^^^^^^^^^^^^^^^^
...
97  |     BillingPortalSession, BillingPortalConfiguration, 
Invoice, InvoiceCreateParams,
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^  
^^^^^^^
98  |     InvoiceItem, InvoiceItemCreateParams, 
SubscriptionSchedule, SubscriptionScheduleCreateParams,
    |     ^^^^^^^^^^^                           
^^^^^^^^^^^^^^^^^^^^
99  |     UsageRecord, UsageRecordCreateParams, Plan, 
PlanCreateParams,
    |     ^^^^^^^^^^^                           ^^^^
...
103 |     TaxRate, TaxRateCreateParams, TaxCode, 
TaxCodeListParams,
    |     ^^^^^^^
...
123 |     Source, SourceCreateParams, SourceUpdateParams,
    |     ^^^^^^
...
126 |     Token, TokenCreateParams,
    |     ^^^^^
...
129 |     Coupon, CouponCreateParams, PromotionCode, 
PromotionCodeCreateParams,
    |     ^^^^^^                      ^^^^^^^^^^^^^
...
132 |     Review, ReviewListParams, ReviewApproveParams,
    |     ^^^^^^
...
135 |     Topup, TopupCreateParams, TopupListParams,
    |     ^^^^^
...
138 |     Mandate, MandateRetrieveParams,
    |     ^^^^^^^
...
147 |     Quote, QuoteCreateParams, QuoteListParams, 
QuoteFinalizeParams,
    |     ^^^^^

warning: unused import: `DateTime`
 --> src\services\transaction.rs:4:14
  |
4 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `futures_util::TryStreamExt`
  --> src\services\transaction.rs:10:5
   |
10 | use futures_util::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\uploader.rs:11:11
   |
11 | use log::{error, info, warn};
   |           ^^^^^

warning: unused import: `actix_web::web`
 --> src\services\webhook.rs:2:5
  |
2 | use actix_web::web;
  |     ^^^^^^^^^^^^^^

warning: unused import: `anyhow`
 --> src\services\withdrawal.rs:4:31
  |
4 | use anyhow::{Context, Result, anyhow};
  |                               ^^^^^^

warning: unused import: `Document`
 --> src\services\withdrawal.rs:7:41
  |
7 | use mongodb::bson::{doc, oid::ObjectId, Document};
  |                                         ^^^^^^^^

warning: unused import: `NaiveDateTime`
 --> src\utils\date_utils.rs:1:45
  |
1 | use chrono::{self, DateTime, Utc, TimeZone, 
NaiveDateTime};
  |                                             
^^^^^^^^^^^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:35:24
    |
35  |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
36  | |                 messages::UNAUTHORIZED,
37  | |                 "Admin access required",
38  | |                 vec![suggestions::CONTACT_SUPPORT]
39  | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:35:20
    |
35  |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
36  | ||                 messages::UNAUTHORIZED,
37  | ||                 "Admin access required",
38  | ||                 vec![suggestions::CONTACT_SUPPORT]
39  | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:76:24
    |
76  |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
77  | |                 messages::UNAUTHORIZED,
78  | |                 "Admin access required",
79  | |                 vec![suggestions::CONTACT_SUPPORT]
80  | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:76:20
    |
76  |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
77  | ||                 messages::UNAUTHORIZED,
78  | ||                 "Admin access required",
79  | ||                 vec![suggestions::CONTACT_SUPPORT]
80  | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:105:24
    |
105 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
106 | |                 messages::UNAUTHORIZED,
107 | |                 "Admin access required",
108 | |                 vec![suggestions::CONTACT_SUPPORT]
109 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:105:20
    |
105 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
106 | ||                 messages::UNAUTHORIZED,
107 | ||                 "Admin access required",
108 | ||                 vec![suggestions::CONTACT_SUPPORT]
109 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:128:24
    |
128 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
129 | |                 messages::UNAUTHORIZED,
130 | |                 "Admin access required",
131 | |                 vec![suggestions::CONTACT_SUPPORT]
132 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:128:20
    |
128 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
129 | ||                 messages::UNAUTHORIZED,
130 | ||                 "Admin access required",
131 | ||                 vec![suggestions::CONTACT_SUPPORT]
132 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:151:24
    |
151 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
152 | |                 messages::UNAUTHORIZED,
153 | |                 "Admin access required",
154 | |                 vec![suggestions::CONTACT_SUPPORT]
155 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:151:20
    |
151 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
152 | ||                 messages::UNAUTHORIZED,
153 | ||                 "Admin access required",
154 | ||                 vec![suggestions::CONTACT_SUPPORT]
155 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:174:24
    |
174 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
175 | |                 messages::UNAUTHORIZED,
176 | |                 "Admin access required",
177 | |                 vec![suggestions::CONTACT_SUPPORT]
178 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:174:20
    |
174 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
175 | ||                 messages::UNAUTHORIZED,
176 | ||                 "Admin access required",
177 | ||                 vec![suggestions::CONTACT_SUPPORT]
178 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:215:24
    |
215 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
216 | |                 messages::UNAUTHORIZED,
217 | |                 "Admin access required",
218 | |                 vec![suggestions::CONTACT_SUPPORT]
219 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:215:20
    |
215 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
216 | ||                 messages::UNAUTHORIZED,
217 | ||                 "Admin access required",
218 | ||                 vec![suggestions::CONTACT_SUPPORT]
219 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:244:24
    |
244 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
245 | |                 messages::UNAUTHORIZED,
246 | |                 "Admin access required",
247 | |                 vec![suggestions::CONTACT_SUPPORT]
248 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:244:20
    |
244 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
245 | ||                 messages::UNAUTHORIZED,
246 | ||                 "Admin access required",
247 | ||                 vec![suggestions::CONTACT_SUPPORT]
248 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:273:24
    |
273 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
274 | |                 messages::UNAUTHORIZED,
275 | |                 "Admin access required",
276 | |                 vec![suggestions::CONTACT_SUPPORT]
277 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:273:20
    |
273 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
274 | ||                 messages::UNAUTHORIZED,
275 | ||                 "Admin access required",
276 | ||                 vec![suggestions::CONTACT_SUPPORT]
277 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:305:24
    |
305 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
306 | |                 messages::UNAUTHORIZED,
307 | |                 "Admin access required",
308 | |                 vec![suggestions::CONTACT_SUPPORT]
309 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:305:20
    |
305 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
306 | ||                 messages::UNAUTHORIZED,
307 | ||                 "Admin access required",
308 | ||                 vec![suggestions::CONTACT_SUPPORT]
309 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:334:24
    |
334 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
335 | |                 messages::UNAUTHORIZED,
336 | |                 "Admin access required",
337 | |                 vec![suggestions::CONTACT_SUPPORT]
338 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:334:20
    |
334 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
335 | ||                 messages::UNAUTHORIZED,
336 | ||                 "Admin access required",
337 | ||                 vec![suggestions::CONTACT_SUPPORT]
338 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:358:24
    |
358 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
359 | |                 messages::UNAUTHORIZED,
360 | |                 "Admin access required",
361 | |                 vec![suggestions::CONTACT_SUPPORT]
362 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:358:20
    |
358 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
359 | ||                 messages::UNAUTHORIZED,
360 | ||                 "Admin access required",
361 | ||                 vec![suggestions::CONTACT_SUPPORT]
362 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\admin_controller.rs:380:24
    |
380 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
381 | |                 messages::UNAUTHORIZED,
382 | |                 "Admin access required",
383 | |                 vec![suggestions::CONTACT_SUPPORT]
384 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\admin_controller.rs:380:20
    |
380 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
381 | ||                 messages::UNAUTHORIZED,
382 | ||                 "Admin access required",
383 | ||                 vec![suggestions::CONTACT_SUPPORT]
384 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0271]: expected `{closure@auth_controller.rs:85:25}` 
to return `Result<_, Error<_>>`, but it returns `{async 
block@src\controllers\auth_controller.rs:85:28: 85:33}`
  --> src\controllers\auth_controller.rs:85:28
   |
85 |           let find_user = || async {
   |  _________________________--_^
   | |                         |
   | |                         this closure
86 | |             data.db.collection::<User>("users")
87 | |                 .find_one(doc! { "email": &email }, 
None)
88 | |                 .await
89 | |         };
   | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
90 |
91 |           let user = match 
retry(ExponentialBackoff::default(), find_user).await {
   |                            -----                       
         --------- closure used here
   |                            |
   |                            required by a bound 
introduced by this call
   |
   = note:       expected enum `Result<_, 
backoff::Error<_>>`
           found `async` block `{async 
block@src\controllers\auth_controller.rs:85:28: 85:33}`
note: required by a bound in `backoff::retry`
  --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949
cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
   |
23 | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
   |        ----- required by a bound in this function
24 | where
25 |     F: FnMut() -> Result<T, Error<E>>,
   |                   ^^^^^^^^^^^^^^^^^^^ required by this 
bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
  --> src\controllers\auth_controller.rs:91:74
   |
91 |         let user = match 
retry(ExponentialBackoff::default(), find_user).await {
   |                          
------------------------------------------------^^^^^
   |                          |                             
                 ||
   |                          |                             
                 |`Result<_, backoff::Error<_>>` is not a 
future
   |                          |                             
                 help: remove the `.await`
   |                          this call returns `Result<_, 
backoff::Error<_>>`
   |
   = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
   = note: Result<_, backoff::Error<_>> must be a future or 
must implement `IntoFuture` to be awaited
   = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@auth_controller.rs:134:35}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\auth_controller.rs:134:38: 134:43}`
   --> src\controllers\auth_controller.rs:134:38
    |
134 |               let update_attempts = || async {
    |  ___________________________________--_^
    | |                                   |
    | |                                   this closure
135 | |                 data.db.collection::<User>("users")
136 | |                     .update_one(
137 | |                         doc! { "_id": 
user.id.unwrap() },
...   |
144 | |                     .await
145 | |             };
    | |_____________^ expected `Result<_, Error<_>>`, found 
`async` block
146 |
147 |               let _ = 
retry(ExponentialBackoff::default(), update_attempts).await;
    |                       -----                           
     --------------- closure used here
    |                       |
    |                       required by a bound introduced 
by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\auth_controller.rs:134:38: 134:43}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\auth_controller.rs:147:75
    |
147 |             let _ = 
retry(ExponentialBackoff::default(), update_attempts).await;
    |                     
------------------------------------------------------^^^^^
    |                     |                                 
                   ||
    |                     |                                 
                   |`Result<_, backoff::Error<_>>` is not a 
future
    |                     |                                 
                   help: remove the `.await`
    |                     this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@auth_controller.rs:152:36}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\auth_controller.rs:152:39: 152:44}`
   --> src\controllers\auth_controller.rs:152:39
    |
152 |                   let lock_account = || async {
    |  ____________________________________--_^
    | |                                    |
    | |                                    this closure
153 | |                     
data.db.collection::<User>("users")
154 | |                         .update_one(
155 | |                             doc! { "_id": 
user.id.unwrap() },
...   |
164 | |                         .await
165 | |                 };
    | |_________________^ expected `Result<_, Error<_>>`, 
found `async` block
166 |
167 |                   let _ = 
retry(ExponentialBackoff::default(), lock_account).await;
    |                           -----                       
         ------------ closure used here
    |                           |
    |                           required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\auth_controller.rs:152:39: 152:44}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\auth_controller.rs:167:76
    |
167 |                 let _ = 
retry(ExponentialBackoff::default(), lock_account).await;
    |                         
---------------------------------------------------^^^^^
    |                         |                             
                    ||
    |                         |                             
                    |`Result<_, backoff::Error<_>>` is not 
a future
    |                         |                             
                    help: remove the `.await`
    |                         this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@auth_controller.rs:192:30}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\auth_controller.rs:192:33: 192:38}`
   --> src\controllers\auth_controller.rs:192:33
    |
192 |           let reset_attempts = || async {
    |  ______________________________--_^
    | |                              |
    | |                              this closure
193 | |             data.db.collection::<User>("users")
194 | |                 .update_one(
195 | |                     doc! { "_id": user.id.unwrap() 
},
...   |
206 | |                 .await
207 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
208 |
209 |           let _ = 
retry(ExponentialBackoff::default(), reset_attempts).await;
    |                   -----                               
 -------------- closure used here
    |                   |
    |                   required by a bound introduced by 
this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\auth_controller.rs:192:33: 192:38}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\auth_controller.rs:209:70
    |
209 |         let _ = retry(ExponentialBackoff::default(), 
reset_attempts).await;
    |                 
-----------------------------------------------------^^^^^
    |                 |                                     
              ||
    |                 |                                     
              |`Result<_, backoff::Error<_>>` is not a 
future
    |                 |                                     
              help: remove the `.await`
    |                 this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0599]: no method named `generate_access_token` found 
for enum `Result` in the current scope
   --> src\controllers\auth_controller.rs:215:42
    |
215 |         let access_token = 
token_service.generate_access_token(
    |                            
--------------^^^^^^^^^^^^^^^^^^^^^ method not found in 
`Result<TokenService, Error>`
    |
note: the method `generate_access_token` exists on the type 
`TokenService`
   --> src\services\token.rs:169:5
    |
169 | /     pub fn generate_access_token(
170 | |         &self,
171 | |         grant_type: &str,
172 | |         client_id: &str,
173 | |         client_secret: &str,
174 | |         grant_token: &str,
175 | |     ) -> Result<(String, String)> {
    | |_________________________________^
help: consider using `Result::expect` to unwrap the 
`TokenService` value, panicking if the value is a 
`Result::Err`
    |
215 |         let access_token = 
token_service.expect("REASON").generate_access_token(
    |                                         
+++++++++++++++++

error[E0599]: no method named `generate_refresh_token` 
found for enum `Result` in the current scope
   --> src\controllers\auth_controller.rs:223:32
    |
223 |             Some(token_service.generate_refresh_token(
    |                  --------------^^^^^^^^^^^^^^^^^^^^^^ 
method not found in `Result<TokenService, Error>`

error[E0599]: no method named `generate_refresh_token` 
found for enum `Result` in the current scope
   --> src\controllers\auth_controller.rs:230:32
    |
230 |             Some(token_service.generate_refresh_token(
    |                  --------------^^^^^^^^^^^^^^^^^^^^^^ 
method not found in `Result<TokenService, Error>`

error[E0271]: expected 
`{closure@auth_controller.rs:284:43}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\auth_controller.rs:284:46: 284:51}`
   --> src\controllers\auth_controller.rs:284:46
    |
284 |                       let blacklist_token = || async {
    |  ___________________________________________--_^
    | |                                           |
    | |                                           this 
closure
285 | |                         
data.db.collection("token_blacklist")
286 | |                             .insert_one(doc! {
287 | |                                 "token": token,
...   |
291 | |                             .await
292 | |                     };
    | |_____________________^ expected `Result<_, 
Error<_>>`, found `async` block
293 |
294 |                       let _ = 
retry(ExponentialBackoff::default(), blacklist_token).await;
    |                               -----                   
             --------------- closure used here
    |                               |
    |                               required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\auth_controller.rs:284:46: 284:51}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\auth_controller.rs:294:83
    |
294 |                     let _ = 
retry(ExponentialBackoff::default(), blacklist_token).await;
    |                             
------------------------------------------------------^^^^^
    |                             |                         
                           ||
    |                             |                         
                           |`Result<_, backoff::Error<_>>` 
is not a future
    |                             |                         
                           help: remove the `.await`
    |                             this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0599]: no method named `validate_refresh_token` 
found for enum `Result` in the current scope
   --> src\controllers\auth_controller.rs:330:49
    |
330 |         let decoded_token = match 
token_service.validate_refresh_token(&req.refresh_token) {
    |                                                 
^^^^^^^^^^^^^^^^^^^^^^ method not found in 
`Result<TokenService, Error>`

error[E0599]: no method named `generate_access_token` found 
for enum `Result` in the current scope
   --> src\controllers\auth_controller.rs:345:42
    |
345 |         let access_token = 
token_service.generate_access_token(
    |                            
--------------^^^^^^^^^^^^^^^^^^^^^ method not found in 
`Result<TokenService, Error>`
    |
note: the method `generate_access_token` exists on the type 
`TokenService`
   --> src\services\token.rs:169:5
    |
169 | /     pub fn generate_access_token(
170 | |         &self,
171 | |         grant_type: &str,
172 | |         client_id: &str,
173 | |         client_secret: &str,
174 | |         grant_token: &str,
175 | |     ) -> Result<(String, String)> {
    | |_________________________________^
help: consider using `Result::expect` to unwrap the 
`TokenService` value, panicking if the value is a 
`Result::Err`
    |
345 |         let access_token = 
token_service.expect("REASON").generate_access_token(
    |                                         
+++++++++++++++++

warning: unused variable: `data`
   --> src\controllers\auth_controller.rs:365:9
    |
365 |         data: web::Data<AppState>,
    |         ^^^^ help: if this is intentional, prefix it 
with an underscore: `_data`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `req`
   --> src\controllers\auth_controller.rs:366:9
    |
366 |         req: web::Json<ForgotPasswordRequest>,
    |         ^^^ help: if this is intentional, prefix it 
with an underscore: `_req`

warning: unused variable: `http_req`
   --> src\controllers\auth_controller.rs:367:9
    |
367 |         http_req: HttpRequest,
    |         ^^^^^^^^ help: if this is intentional, prefix 
it with an underscore: `_http_req`

warning: unused variable: `data`
   --> src\controllers\auth_controller.rs:381:9
    |
381 |         data: web::Data<AppState>,
    |         ^^^^ help: if this is intentional, prefix it 
with an underscore: `_data`

warning: unused variable: `req`
   --> src\controllers\auth_controller.rs:382:9
    |
382 |         req: web::Json<ResetPasswordRequest>,
    |         ^^^ help: if this is intentional, prefix it 
with an underscore: `_req`

warning: unused variable: `http_req`
   --> src\controllers\auth_controller.rs:383:9
    |
383 |         http_req: HttpRequest,
    |         ^^^^^^^^ help: if this is intentional, prefix 
it with an underscore: `_http_req`

warning: unused variable: `data`
   --> src\controllers\auth_controller.rs:397:9
    |
397 |         data: web::Data<AppState>,
    |         ^^^^ help: if this is intentional, prefix it 
with an underscore: `_data`

warning: unused variable: `req`
   --> src\controllers\auth_controller.rs:398:9
    |
398 |         req: web::Json<VerifyEmailRequest>,
    |         ^^^ help: if this is intentional, prefix it 
with an underscore: `_req`

warning: unused variable: `http_req`
   --> src\controllers\auth_controller.rs:399:9
    |
399 |         http_req: HttpRequest,
    |         ^^^^^^^^ help: if this is intentional, prefix 
it with an underscore: `_http_req`

warning: unused variable: `data`
   --> src\controllers\auth_controller.rs:413:9
    |
413 |         data: web::Data<AppState>,
    |         ^^^^ help: if this is intentional, prefix it 
with an underscore: `_data`

warning: unused variable: `http_req`
   --> src\controllers\auth_controller.rs:414:9
    |
414 |         http_req: HttpRequest,
    |         ^^^^^^^^ help: if this is intentional, prefix 
it with an underscore: `_http_req`

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:58:24
    |
58  |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
59  | |                 messages::UNAUTHORIZED,
60  | |                 "Insufficient permissions for 
cardholder creation",
61  | |                 vec![suggestions::CONTACT_SUPPORT]
62  | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\developer_api.rs:58:20
    |
58  |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
59  | ||                 messages::UNAUTHORIZED,
60  | ||                 "Insufficient permissions for 
cardholder creation",
61  | ||                 vec![suggestions::CONTACT_SUPPORT]
62  | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:67:24
    |
67  |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |                    |
    |                    arguments to this enum variant are 
incorrect
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\developer_api.rs:67:20
    |
67  |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    ^^^^-------------------------------
---------------------------------^
    |                        |
    |                        this argument influences the 
type of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
    --> src\controllers\developer_api.rs:70:59
     |
70   |         match 
controller.stripe_service.create_cardholder(request).await {
     |                                         
----------------- ^^^^^^^ expected 
`services::stripe::UserDetails`, found 
`models::user::UserDetails`
     |                                         |
     |                                         arguments to 
this method are incorrect
     |
     = note: `models::user::UserDetails` and 
`services::stripe::UserDetails` have similar names, but are 
actually distinct types
note: `models::user::UserDetails` is defined in module 
`crate::models::user` of the current crate
    --> src\models\user.rs:136:1
     |
136  | pub struct UserDetails {
     | ^^^^^^^^^^^^^^^^^^^^^^
note: `services::stripe::UserDetails` is defined in module 
`crate::services::stripe` of the current crate
    --> src\services\stripe.rs:990:1
     |
990  | pub struct UserDetails {
     | ^^^^^^^^^^^^^^^^^^^^^^
note: method defined here
    --> src\services\stripe.rs:2278:18
     |
2278 |     pub async fn create_cardholder(&self, 
user_details: UserDetails) -> Result<Cardholder> {
     |                  ^^^^^^^^^^^^^^^^^        
-------------------------

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:87:21
    |
87  |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
88  | |                     
messages::CARDHOLDER_CREATION_FAILED,
89  | |                     &e.to_string(),
90  | |                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
91  | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\developer_api.rs:87:17
    |
87  |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
88  | ||                     
messages::CARDHOLDER_CREATION_FAILED,
89  | ||                     &e.to_string(),
90  | ||                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
91  | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:242:24
    |
242 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
243 | |                 messages::UNAUTHORIZED,
244 | |                 "Insufficient permissions to list 
cards",
245 | |                 vec![suggestions::CONTACT_SUPPORT]
246 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\developer_api.rs:242:20
    |
242 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
243 | ||                 messages::UNAUTHORIZED,
244 | ||                 "Insufficient permissions to list 
cards",
245 | ||                 vec![suggestions::CONTACT_SUPPORT]
246 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:269:21
    |
269 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
270 | |                     messages::INTERNAL_SERVER_ERROR,
271 | |                     &e.to_string(),
272 | |                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
273 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\developer_api.rs:269:17
    |
269 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
270 | ||                     
messages::INTERNAL_SERVER_ERROR,
271 | ||                     &e.to_string(),
272 | ||                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
273 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:286:24
    |
286 |               return Err(ResponseHelper::forbidden(
    |  ____________________---_^
    | |                    |
    | |                    arguments to this enum variant 
are incorrect
287 | |                 messages::UNAUTHORIZED,
288 | |                 "Insufficient permissions to 
configure webhooks",
289 | |                 vec![suggestions::CONTACT_SUPPORT]
290 | |             ));
    | |_____________^ expected `(StatusCode, Json<Value>)`, 
found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\developer_api.rs:286:20
    |
286 |                return Err(ResponseHelper::forbidden(
    |   ____________________^   -
    |  |________________________|
287 | ||                 messages::UNAUTHORIZED,
288 | ||                 "Insufficient permissions to 
configure webhooks",
289 | ||                 vec![suggestions::CONTACT_SUPPORT]
290 | ||             ));
    | ||_____________-^
    | |______________|
    |                this argument influences the type of 
`Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:295:24
    |
295 |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |                    |
    |                    arguments to this enum variant are 
incorrect
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\developer_api.rs:295:20
    |
295 |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    ^^^^-------------------------------
---------------------------------^
    |                        |
    |                        this argument influences the 
type of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:305:25
    |
305 |             created_at: Some(chrono::Utc::now()),
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^ 
expected `DateTime<Utc>`, found `Option<DateTime<Utc>>`
    |
    = note: expected struct `chrono::DateTime<_>`
                 found enum 
`std::option::Option<chrono::DateTime<_>>`

error[E0063]: missing fields `description`, 
`failure_count`, `last_triggered` and 1 other field in 
initializer of `models::webhook::WebhookConfig`
   --> src\controllers\developer_api.rs:298:30
    |
298 |         let webhook_config = WebhookConfig {
    |                              ^^^^^^^^^^^^^ missing 
`description`, `failure_count`, `last_triggered` and 1 
other field

error[E0308]: mismatched types
   --> src\controllers\developer_api.rs:321:21
    |
321 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
322 | |                     messages::INTERNAL_SERVER_ERROR,
323 | |                     &e.to_string(),
324 | |                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
325 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\developer_api.rs:321:17
    |
321 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
322 | ||                     
messages::INTERNAL_SERVER_ERROR,
323 | ||                     &e.to_string(),
324 | ||                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
325 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0271]: expected 
`{closure@developer_api_controller.rs:179:28}` to return 
`Result<_, Error<_>>`, but it returns `{async 
block@src\controllers\developer_api_controller.rs:179:31: 
179:36}`
   --> src\controllers\developer_api_controller.rs:179:31
    |
179 |           let find_account = || async {
    |  ____________________________--_^
    | |                            |
    | |                            this closure
180 | |             
data.db.collection::<Account>("accounts")
181 | |                 .find_one(doc! { "accountNumber": 
&sanitized_account_number }, None)
182 | |                 .await
183 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
184 |
185 |           let account = match 
retry(ExponentialBackoff::default(), find_account).await {
    |                               -----                   
             ------------ closure used here
    |                               |
    |                               required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\developer_api_controller.rs:179:31: 
179:36}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\developer_api_controller.rs:185:80
    |
185 |         let account = match 
retry(ExponentialBackoff::default(), find_account).await {
    |                             
---------------------------------------------------^^^^^
    |                             |                         
                        ||
    |                             |                         
                        |`Result<_, backoff::Error<_>>` is 
not a future
    |                             |                         
                        help: remove the `.await`
    |                             this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@developer_api_controller.rs:211:25}` to return 
`Result<_, Error<_>>`, but it returns `{async 
block@src\controllers\developer_api_controller.rs:211:28: 
211:33}`
   --> src\controllers\developer_api_controller.rs:211:28
    |
211 |           let find_user = || async {
    |  _________________________--_^
    | |                         |
    | |                         this closure
212 | |             data.db.collection::<User>("users")
213 | |                 .find_one(doc! { "_id": 
account.user_id }, None)
214 | |                 .await
215 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
216 |
217 |           let user = match 
retry(ExponentialBackoff::default(), find_user).await {
    |                            -----                      
          --------- closure used here
    |                            |
    |                            required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\developer_api_controller.rs:211:28: 
211:33}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\developer_api_controller.rs:217:74
    |
217 |         let user = match 
retry(ExponentialBackoff::default(), find_user).await {
    |                          
------------------------------------------------^^^^^
    |                          |                            
                  ||
    |                          |                            
                  |`Result<_, backoff::Error<_>>` is not a 
future
    |                          |                            
                  help: remove the `.await`
    |                          this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0599]: no method named `extensions` found for struct 
`HttpRequest` in the current scope
   --> src\controllers\developer_api_controller.rs:280:35
    |
280 |         let extensions = http_req.extensions();
    |                                   ^^^^^^^^^^
    |
   ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\actix-http-3.10.0\src\http_message.rs:29:8
    |
29  |     fn extensions(&self) -> Ref<'_, Extensions>;
    |        ---------- the method is available for 
`HttpRequest` here
    |
    = help: items from traits can only be used if the trait 
is in scope
help: trait `HttpMessage` which provides `extensions` is 
implemented but not in scope; perhaps you want to import it
    |
1   + use actix_web::HttpMessage;
    |
help: there is a method `extensions_mut` with a similar name
    |
280 |         let extensions = http_req.extensions_mut();
    |                                             ++++

error[E0061]: this method takes 0 arguments but 1 argument 
was supplied
   --> src\controllers\developer_api_controller.rs:318:37
    |
318 |         let session = match 
data.db.start_session(None).await {
    |                                     ^^^^^^^^^^^^^ 
---- unexpected argument of type `std::option::Option<_>`
    |
note: method defined here
   --> src\database.rs:56:18
    |
56  |     pub async fn start_session(&self) -> 
Result<mongodb::ClientSession> {
    |                  ^^^^^^^^^^^^^
help: remove the extra argument
    |
318 -         let session = match 
data.db.start_session(None).await {
318 +         let session = match 
data.db.start_session().await {
    |

error[E0271]: expected 
`{closure@developer_api_controller.rs:334:28}` to return 
`Result<_, Error<_>>`, but it returns `{async 
block@src\controllers\developer_api_controller.rs:334:31: 
334:36}`
   --> src\controllers\developer_api_controller.rs:334:31
    |
334 |           let find_account = || async {
    |  ____________________________--_^
    | |                            |
    | |                            this closure
335 | |             
data.db.collection::<Account>("accounts")
336 | |                 .find_one(doc! { "accountNumber": 
&sanitized_account_number }, None)
337 | |                 .await
338 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
339 |
340 |           let mut account = match 
retry(ExponentialBackoff::default(), find_account).await {
    |                                   -----               
                 ------------ closure used here
    |                                   |
    |                                   required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\developer_api_controller.rs:334:31: 
334:36}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\developer_api_controller.rs:340:84
    |
340 |         let mut account = match 
retry(ExponentialBackoff::default(), find_account).await {
    |                                 
---------------------------------------------------^^^^^
    |                                 |                     
                            ||
    |                                 |                     
                            |`Result<_, backoff::Error<_>>` 
is not a future
    |                                 |                     
                            help: remove the `.await`
    |                                 this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@developer_api_controller.rs:397:30}` to return 
`Result<_, Error<_>>`, but it returns `{async 
block@src\controllers\developer_api_controller.rs:397:33: 
397:38}`
   --> src\controllers\developer_api_controller.rs:397:33
    |
397 |           let update_account = || async {
    |  ______________________________--_^
    | |                              |
    | |                              this closure
398 | |             
data.db.collection::<Account>("accounts")
399 | |                 .replace_one(
400 | |                     doc! { "accountNumber": 
&sanitized_account_number },
...   |
404 | |                 .await
405 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
406 |
407 |           if let Err(e) = 
retry(ExponentialBackoff::default(), update_account).await {
    |                           -----                       
         -------------- closure used here
    |                           |
    |                           required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\developer_api_controller.rs:397:33: 
397:38}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\developer_api_controller.rs:407:78
    |
407 |         if let Err(e) = 
retry(ExponentialBackoff::default(), update_account).await {
    |                         
-----------------------------------------------------^^^^^
    |                         |                             
                      ||
    |                         |                             
                      |`Result<_, backoff::Error<_>>` is 
not a future
    |                         |                             
                      help: remove the `.await`
    |                         this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0560]: struct `models::transaction::Transaction` has 
no field named `transaction_type`
   --> src\controllers\developer_api_controller.rs:424:13
    |
424 |             transaction_type: "DEPOSIT".to_string(),
    |             ^^^^^^^^^^^^^^^^ 
`models::transaction::Transaction` does not have this field
    |
    = note: available fields are: `type_`, `metadata`, 
`processed_at`, `card_transaction`, `reference` ... and 12 
others

error[E0308]: mismatched types
   --> src\controllers\developer_api_controller.rs:428:21
    |
428 |             status: "COMPLETED".to_string(),
    |                     ^^^^^^^^^^^^^^^^^^^^^^^ expected 
`TransactionStatus`, found `String`

error[E0308]: mismatched types
   --> src\controllers\developer_api_controller.rs:429:21
    |
429 |             sender: None,
    |                     ^^^^ expected 
`TransactionParticipant`, found `Option<_>`
    |
    = note: expected struct `TransactionParticipant`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\controllers\developer_api_controller.rs:437:22
    |
437 |             receipt: sanitized_reference.clone(),
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^ 
expected `Option<String>`, found `String`
    |
    = note: expected enum 
`std::option::Option<std::string::String>`
             found struct `std::string::String`
help: try wrapping the expression in `Some`
    |
437 |             receipt: 
Some(sanitized_reference.clone()),
    |                      +++++                           +

error[E0560]: struct `models::transaction::Transaction` has 
no field named `security_info`
   --> src\controllers\developer_api_controller.rs:438:13
    |
438 |             security_info: Some(json!({
    |             ^^^^^^^^^^^^^ 
`models::transaction::Transaction` does not have this field
    |
    = note: available fields are: `type_`, `metadata`, 
`processed_at`, `card_transaction`, `reference` ... and 12 
others

error[E0271]: expected 
`{closure@developer_api_controller.rs:449:34}` to return 
`Result<_, Error<_>>`, but it returns `{async 
block@src\controllers\developer_api_controller.rs:449:37: 
449:42}`
   --> src\controllers\developer_api_controller.rs:449:37
    |
449 |           let insert_transaction = || async {
    |  __________________________________--_^
    | |                                  |
    | |                                  this closure
450 | |             
data.db.collection::<Transaction>("transactions")
451 | |                 .insert_one(&transaction, None)
452 | |                 .await
453 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
454 |
455 |           let transaction_result = match 
retry(ExponentialBackoff::default(), 
insert_transaction).await {
    |                                          -----        
                        ------------------ closure used here
    |                                          |
    |                                          required by 
a bound introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\developer_api_controller.rs:449:37: 
449:42}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\developer_api_controller.rs:455:97
    |
455 |         let transaction_result = match 
retry(ExponentialBackoff::default(), 
insert_transaction).await {
    |                                        ---------------
------------------------------------------^^^^^
    |                                        |              
                                         ||
    |                                        |              
                                         |`Result<_, 
backoff::Error<_>>` is not a future
    |                                        |              
                                         help: remove the 
`.await`
    |                                        this call 
returns `Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0308]: mismatched types
   --> src\controllers\developer_api_controller.rs:485:13
    |
474 |         let _ = webhook_service.trigger_webhook(
    |                                 --------------- 
arguments to this method are incorrect
...
485 |             &data.db,
    |             ^^^^^^^^ expected `&Database`, found 
`&Arc<DatabaseService>`
    |
    = note: expected reference `&Database`
               found reference 
`&Arc<database::DatabaseService>`
note: method defined here
   --> src\services\webhook.rs:35:18
    |
35  |     pub async fn trigger_webhook(
    |                  ^^^^^^^^^^^^^^^
...
40  |         db: &Database,
    |         -------------

warning: unused variable: `status_code`
  --> src\controllers\exchange_controller.rs:66:14
   |
66 |         let (status_code, response) = 
ResponseHelper::ok(
   |              ^^^^^^^^^^^ help: if this is intentional, 
prefix it with an underscore: `_status_code`

warning: unused variable: `status_code`
  --> src\controllers\helper_controller.rs:72:14
   |
72 |         let (status_code, response) = 
ResponseHelper::ok(
   |              ^^^^^^^^^^^ help: if this is intentional, 
prefix it with an underscore: `_status_code`

error[E0271]: expected 
`{closure@oauth_controller.rs:173:25}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\oauth_controller.rs:173:28: 173:33}`
   --> src\controllers\oauth_controller.rs:173:28
    |
173 |           let find_user = || async {
    |  _________________________--_^
    | |                         |
    | |                         this closure
174 | |             data.db.collection::<User>("users")
175 | |                 .find_one(doc! { "_id": user_oid }, 
None)
176 | |                 .await
177 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
178 |
179 |           let user = match 
retry(ExponentialBackoff::default(), find_user).await {
    |                            -----                      
          --------- closure used here
    |                            |
    |                            required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\oauth_controller.rs:173:28: 173:33}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\oauth_controller.rs:179:74
    |
179 |         let user = match 
retry(ExponentialBackoff::default(), find_user).await {
    |                          
------------------------------------------------^^^^^
    |                          |                            
                  ||
    |                          |                            
                  |`Result<_, backoff::Error<_>>` is not a 
future
    |                          |                            
                  help: remove the `.await`
    |                          this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@oauth_controller.rs:230:29}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\oauth_controller.rs:230:32: 230:37}`
   --> src\controllers\oauth_controller.rs:230:32
    |
230 |           let insert_client = || async {
    |  _____________________________--_^
    | |                             |
    | |                             this closure
231 | |             
data.db.collection::<OAuthClient>("oauth_clients")
232 | |                 .insert_one(&oauth_client, None)
233 | |                 .await
234 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
235 |
236 |           match retry(ExponentialBackoff::default(), 
insert_client).await {
    |                 -----                                
------------- closure used here
    |                 |
    |                 required by a bound introduced by 
this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\oauth_controller.rs:230:32: 230:37}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\oauth_controller.rs:236:67
    |
236 |         match retry(ExponentialBackoff::default(), 
insert_client).await {
    |               
----------------------------------------------------^^^^^
    |               |                                       
           ||
    |               |                                       
           |`Result<_, backoff::Error<_>>` is not a future
    |               |                                       
           help: remove the `.await`
    |               this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@oauth_controller.rs:291:27}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\oauth_controller.rs:291:30: 291:35}`
   --> src\controllers\oauth_controller.rs:291:30
    |
291 |           let find_client = || async {
    |  ___________________________--_^
    | |                           |
    | |                           this closure
292 | |             
data.db.collection::<OAuthClient>("oauth_clients")
293 | |                 .find_one(doc! { "client_id": 
&query.client_id }, None)
294 | |                 .await
295 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
296 |
297 |           let client = match 
retry(ExponentialBackoff::default(), find_client).await {
    |                              -----                    
            ----------- closure used here
    |                              |
    |                              required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\oauth_controller.rs:291:30: 291:35}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\oauth_controller.rs:297:78
    |
297 |         let client = match 
retry(ExponentialBackoff::default(), find_client).await {
    |                            
--------------------------------------------------^^^^^
    |                            |                          
                      ||
    |                            |                          
                      |`Result<_, backoff::Error<_>>` is 
not a future
    |                            |                          
                      help: remove the `.await`
    |                            this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@oauth_controller.rs:391:31}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\oauth_controller.rs:391:34: 391:39}`
   --> src\controllers\oauth_controller.rs:391:34
    |
391 |               let insert_code = || async {
    |  _______________________________--_^
    | |                               |
    | |                               this closure
392 | |                 
data.db.collection::<OAuthCode>("oauth_codes")
393 | |                     .insert_one(&oauth_code, None)
394 | |                     .await
395 | |             };
    | |_____________^ expected `Result<_, Error<_>>`, found 
`async` block
396 |
397 |               match 
retry(ExponentialBackoff::default(), insert_code).await {
    |                     -----                             
   ----------- closure used here
    |                     |
    |                     required by a bound introduced by 
this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\oauth_controller.rs:391:34: 391:39}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\oauth_controller.rs:397:69
    |
397 |             match 
retry(ExponentialBackoff::default(), insert_code).await {
    |                   
--------------------------------------------------^^^^^
    |                   |                                   
             ||
    |                   |                                   
             |`Result<_, backoff::Error<_>>` is not a future
    |                   |                                   
             help: remove the `.await`
    |                   this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@oauth_controller.rs:465:27}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\oauth_controller.rs:465:30: 465:35}`
   --> src\controllers\oauth_controller.rs:465:30
    |
465 |           let find_client = || async {
    |  ___________________________--_^
    | |                           |
    | |                           this closure
466 | |             
data.db.collection::<OAuthClient>("oauth_clients")
467 | |                 .find_one(doc! { "client_id": 
&req.client_id }, None)
468 | |                 .await
469 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
470 |
471 |           let client = match 
retry(ExponentialBackoff::default(), find_client).await {
    |                              -----                    
            ----------- closure used here
    |                              |
    |                              required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\oauth_controller.rs:465:30: 465:35}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\oauth_controller.rs:471:78
    |
471 |         let client = match 
retry(ExponentialBackoff::default(), find_client).await {
    |                            
--------------------------------------------------^^^^^
    |                            |                          
                      ||
    |                            |                          
                      |`Result<_, backoff::Error<_>>` is 
not a future
    |                            |                          
                      help: remove the `.await`
    |                            this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0599]: no function or associated item named 
`handle_client_credentials_grant` found for struct 
`OAuthController` in the current scope
   --> src\controllers\oauth_controller.rs:526:23
    |
18  | pub struct OAuthController;
    | -------------------------- function or associated 
item `handle_client_credentials_grant` not found for this 
struct
...
526 |                 
Self::handle_client_credentials_grant(&data, &req, 
&client).await
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ 
function or associated item not found in `OAuthController`

error[E0599]: no function or associated item named 
`handle_refresh_token_grant` found for struct 
`OAuthController` in the current scope
   --> src\controllers\oauth_controller.rs:529:23
    |
18  | pub struct OAuthController;
    | -------------------------- function or associated 
item `handle_refresh_token_grant` not found for this struct
...
529 |                 
Self::handle_refresh_token_grant(&data, &req, &client).await
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^ 
function or associated item not found in `OAuthController`

error[E0271]: expected 
`{closure@oauth_controller.rs:579:25}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\oauth_controller.rs:579:28: 579:33}`
   --> src\controllers\oauth_controller.rs:579:28
    |
579 |           let find_code = || async {
    |  _________________________--_^
    | |                         |
    | |                         this closure
580 | |             
data.db.collection::<OAuthCode>("oauth_codes")
581 | |                 .find_one(doc! {
582 | |                     "code": code,
...   |
586 | |                 .await
587 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
588 |
589 |           let oauth_code = match 
retry(ExponentialBackoff::default(), find_code).await {
    |                                  -----                
                --------- closure used here
    |                                  |
    |                                  required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\oauth_controller.rs:579:28: 579:33}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\oauth_controller.rs:589:80
    |
589 |         let oauth_code = match 
retry(ExponentialBackoff::default(), find_code).await {
    |                                
------------------------------------------------^^^^^
    |                                |                      
                        ||
    |                                |                      
                        |`Result<_, backoff::Error<_>>` is 
not a future
    |                                |                      
                        help: remove the `.await`
    |                                this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@oauth_controller.rs:639:25}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\oauth_controller.rs:639:28: 639:33}`
   --> src\controllers\oauth_controller.rs:639:28
    |
639 |           let mark_used = || async {
    |  _________________________--_^
    | |                         |
    | |                         this closure
640 | |             
data.db.collection::<OAuthCode>("oauth_codes")
641 | |                 .update_one(
642 | |                     doc! { "code": code },
...   |
646 | |                 .await
647 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
648 |
649 |           if let Err(e) = 
retry(ExponentialBackoff::default(), mark_used).await {
    |                           -----                       
         --------- closure used here
    |                           |
    |                           required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\oauth_controller.rs:639:28: 639:33}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\oauth_controller.rs:649:73
    |
649 |         if let Err(e) = 
retry(ExponentialBackoff::default(), mark_used).await {
    |                         
------------------------------------------------^^^^^
    |                         |                             
                 ||
    |                         |                             
                 |`Result<_, backoff::Error<_>>` is not a 
future
    |                         |                             
                 help: remove the `.await`
    |                         this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0599]: no method named `generate_access_token` found 
for enum `Result` in the current scope
   --> src\controllers\oauth_controller.rs:655:42
    |
655 |         let access_token = 
token_service.generate_access_token(
    |                            
--------------^^^^^^^^^^^^^^^^^^^^^ method not found in 
`Result<TokenService, Error>`
    |
note: the method `generate_access_token` exists on the type 
`TokenService`
   --> src\services\token.rs:169:5
    |
169 | /     pub fn generate_access_token(
170 | |         &self,
171 | |         grant_type: &str,
172 | |         client_id: &str,
173 | |         client_secret: &str,
174 | |         grant_token: &str,
175 | |     ) -> Result<(String, String)> {
    | |_________________________________^
help: consider using `Result::expect` to unwrap the 
`TokenService` value, panicking if the value is a 
`Result::Err`
    |
655 |         let access_token = 
token_service.expect("REASON").generate_access_token(
    |                                         
+++++++++++++++++

error[E0599]: no method named `generate_refresh_token` 
found for enum `Result` in the current scope
   --> src\controllers\oauth_controller.rs:663:32
    |
663 |             Some(token_service.generate_refresh_token(
    |                  --------------^^^^^^^^^^^^^^^^^^^^^^ 
method not found in `Result<TokenService, Error>`

error[E0560]: struct `OAuthToken` has no field named 
`scopes`
   --> src\controllers\oauth_controller.rs:681:13
    |
681 |             scopes: oauth_code.scopes.clone(),
    |             ^^^^^^ unknown field
    |
help: a field with a similar name exists
    |
681 -             scopes: oauth_code.scopes.clone(),
681 +             scope: oauth_code.scopes.clone(),
    |

error[E0560]: struct `OAuthToken` has no field named 
`permissions`
   --> src\controllers\oauth_controller.rs:682:13
    |
682 |             permissions: 
oauth_code.permissions.clone(),
    |             ^^^^^^^^^^^ `OAuthToken` does not have 
this field
    |
    = note: available fields are: `scope`

error[E0560]: struct `OAuthToken` has no field named 
`refresh_expires_at`
   --> src\controllers\oauth_controller.rs:684:13
    |
684 |             refresh_expires_at: 
refresh_token.as_ref().map(|_| now + 
Duration::seconds(2592000)),
    |             ^^^^^^^^^^^^^^^^^^ `OAuthToken` does not 
have this field
    |
    = note: available fields are: `scope`

error[E0271]: expected 
`{closure@oauth_controller.rs:688:28}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\oauth_controller.rs:688:31: 688:36}`
   --> src\controllers\oauth_controller.rs:688:31
    |
688 |           let insert_token = || async {
    |  ____________________________--_^
    | |                            |
    | |                            this closure
689 | |             
data.db.collection::<OAuthToken>("oauth_tokens")
690 | |                 .insert_one(&oauth_token, None)
691 | |                 .await
692 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
693 |
694 |           match retry(ExponentialBackoff::default(), 
insert_token).await {
    |                 -----                                
------------ closure used here
    |                 |
    |                 required by a bound introduced by 
this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\oauth_controller.rs:688:31: 688:36}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\oauth_controller.rs:694:66
    |
694 |         match retry(ExponentialBackoff::default(), 
insert_token).await {
    |               
---------------------------------------------------^^^^^
    |               |                                       
          ||
    |               |                                       
          |`Result<_, backoff::Error<_>>` is not a future
    |               |                                       
          help: remove the `.await`
    |               this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0308]: mismatched types
   --> src\controllers\stripe.rs:42:24
    |
42  |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |                    |
    |                    arguments to this enum variant are 
incorrect
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\stripe.rs:42:20
    |
42  |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    ^^^^-------------------------------
---------------------------------^
    |                        |
    |                        this argument influences the 
type of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
    --> src\controllers\stripe.rs:45:59
     |
45   |         match 
controller.stripe_service.create_cardholder(request).await {
     |                                         
----------------- ^^^^^^^ expected 
`services::stripe::UserDetails`, found 
`models::user::UserDetails`
     |                                         |
     |                                         arguments to 
this method are incorrect
     |
     = note: `models::user::UserDetails` and 
`services::stripe::UserDetails` have similar names, but are 
actually distinct types
note: `models::user::UserDetails` is defined in module 
`crate::models::user` of the current crate
    --> src\models\user.rs:136:1
     |
136  | pub struct UserDetails {
     | ^^^^^^^^^^^^^^^^^^^^^^
note: `services::stripe::UserDetails` is defined in module 
`crate::services::stripe` of the current crate
    --> src\services\stripe.rs:990:1
     |
990  | pub struct UserDetails {
     | ^^^^^^^^^^^^^^^^^^^^^^
note: method defined here
    --> src\services\stripe.rs:2278:18
     |
2278 |     pub async fn create_cardholder(&self, 
user_details: UserDetails) -> Result<Cardholder> {
     |                  ^^^^^^^^^^^^^^^^^        
-------------------------

error[E0308]: mismatched types
   --> src\controllers\stripe.rs:55:21
    |
55  |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
56  | |                     
messages::CARDHOLDER_CREATION_FAILED,
57  | |                     &e.to_string(),
58  | |                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
59  | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\stripe.rs:55:17
    |
55  |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
56  | ||                     
messages::CARDHOLDER_CREATION_FAILED,
57  | ||                     &e.to_string(),
58  | ||                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
59  | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\stripe.rs:129:21
    |
129 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
130 | |                     messages::CARD_BLOCK_FAILED,
131 | |                     &e.to_string(),
132 | |                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
133 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\stripe.rs:129:17
    |
129 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
130 | ||                     messages::CARD_BLOCK_FAILED,
131 | ||                     &e.to_string(),
132 | ||                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
133 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\stripe.rs:154:21
    |
154 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
155 | |                     messages::CARD_UNBLOCK_FAILED,
156 | |                     &e.to_string(),
157 | |                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
158 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\stripe.rs:154:17
    |
154 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
155 | ||                     messages::CARD_UNBLOCK_FAILED,
156 | ||                     &e.to_string(),
157 | ||                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
158 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\stripe.rs:179:21
    |
179 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
180 | |                     messages::CARD_DETAILS_FAILED,
181 | |                     &e.to_string(),
182 | |                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
183 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\stripe.rs:179:17
    |
179 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
180 | ||                     messages::CARD_DETAILS_FAILED,
181 | ||                     &e.to_string(),
182 | ||                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
183 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\stripe.rs:215:21
    |
215 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
216 | |                     
messages::TRANSACTIONS_RETRIEVAL_FAILED,
217 | |                     &e.to_string(),
218 | |                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
219 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\stripe.rs:215:17
    |
215 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
216 | ||                     
messages::TRANSACTIONS_RETRIEVAL_FAILED,
217 | ||                     &e.to_string(),
218 | ||                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
219 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\stripe.rs:232:24
    |
232 |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |                    |
    |                    arguments to this enum variant are 
incorrect
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\stripe.rs:232:20
    |
232 |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    ^^^^-------------------------------
---------------------------------^
    |                        |
    |                        this argument influences the 
type of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\stripe.rs:252:21
    |
252 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
253 | |                     
messages::PAYMENT_INTENT_CREATION_FAILED,
254 | |                     &e.to_string(),
255 | |                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
256 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\stripe.rs:252:17
    |
252 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
253 | ||                     
messages::PAYMENT_INTENT_CREATION_FAILED,
254 | ||                     &e.to_string(),
255 | ||                     
vec![suggestions::CHECK_INPUT_DATA, 
suggestions::TRY_AGAIN_LATER]
256 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0277]: `?` couldn't convert the error to 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
   --> src\controllers\stripe.rs:277:15
    |
271 |               .ok_or_else(|| {
    |  ______________-
272 | |                 ResponseHelper::bad_request(
273 | |                     
messages::MISSING_STRIPE_SIGNATURE,
274 | |                     "Stripe signature header is 
required",
...   |
277 | |             })?;
    | |              -^ unsatisfied trait bound
    | |______________|
    |                this can't be annotated with `?` 
because it has type `Result<_, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>`
    |
    = help: the trait 
`std::convert::From<(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>` is not implemented for 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
    = note: the question mark operation (`?`) implicitly 
performs a conversion on the error value using the `From` 
trait
    = help: the following other types implement trait 
`std::convert::From<T>`:
              `(T, T)` implements `std::convert::From<[T; 
2]>`
              `(T, T, T)` implements 
`std::convert::From<[T; 3]>`
              `(T, T, T, T)` implements 
`std::convert::From<[T; 4]>`
              `(T, T, T, T, T)` implements 
`std::convert::From<[T; 5]>`
              `(T, T, T, T, T, T)` implements 
`std::convert::From<[T; 6]>`
              `(T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 7]>`
              `(T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 8]>`
              `(T, T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 9]>`
            and 6 others
    = note: required for 
`Result<Json<WebhookCallbackResponse>, (StatusCode, 
Json<Value>)>` to implement 
`FromResidual<Result<Infallible, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>>`
    = note: the full name for the type has been written to '
D:\workspace\.upwork\sangapay\sangapay-rust\target\debug\dep
s\sangapay-89f53a53efa19b7f.long-type-14942391310499267503.t
xt'
    = note: consider using `--verbose` to print the full 
type name to the console

error[E0271]: expected 
`{closure@user_controller.rs:112:35}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\user_controller.rs:112:38: 112:43}`
   --> src\controllers\user_controller.rs:112:38
    |
112 |           let check_existing_user = || async {
    |  ___________________________________--_^
    | |                                   |
    | |                                   this closure
113 | |             data.db.collection::<User>("users")
114 | |                 .find_one(doc! {
115 | |                     "$or": [
...   |
120 | |                 .await
121 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
122 |
123 |           let existing_user = match 
retry(ExponentialBackoff::default(), 
check_existing_user).await {
    |                                     -----             
                   ------------------- closure used here
    |                                     |
    |                                     required by a 
bound introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\user_controller.rs:112:38: 112:43}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\user_controller.rs:123:93
    |
123 |         let existing_user = match 
retry(ExponentialBackoff::default(), 
check_existing_user).await {
    |                                   --------------------
--------------------------------------^^^^^
    |                                   |                   
                                     ||
    |                                   |                   
                                     |`Result<_, 
backoff::Error<_>>` is not a future
    |                                   |                   
                                     help: remove the 
`.await`
    |                                   this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0308]: mismatched types
   --> src\controllers\user_controller.rs:172:25
    |
172 |             first_name: first_name.clone(),
    |                         ^^^^^^^^^^^^^^^^^^ expected 
`Option<String>`, found `String`
    |
    = note: expected enum 
`std::option::Option<std::string::String>`
             found struct `std::string::String`
help: try wrapping the expression in `Some`
    |
172 |             first_name: Some(first_name.clone()),
    |                         +++++                  +

error[E0308]: mismatched types
   --> src\controllers\user_controller.rs:173:24
    |
173 |             last_name: last_name.clone(),
    |                        ^^^^^^^^^^^^^^^^^ expected 
`Option<String>`, found `String`
    |
    = note: expected enum 
`std::option::Option<std::string::String>`
             found struct `std::string::String`
help: try wrapping the expression in `Some`
    |
173 |             last_name: Some(last_name.clone()),
    |                        +++++                 +

error[E0308]: mismatched types
   --> src\controllers\user_controller.rs:175:23
    |
175 |             password: password_hash,
    |                       ^^^^^^^^^^^^^ expected 
`Option<String>`, found `String`
    |
    = note: expected enum 
`std::option::Option<std::string::String>`
             found struct `std::string::String`
help: try wrapping the expression in `Some`
    |
175 |             password: Some(password_hash),
    |                       +++++             +

error[E0560]: struct `models::user::User` has no field 
named `phone`
   --> src\controllers\user_controller.rs:176:13
    |
176 |             phone: phone.clone(),
    |             ^^^^^ `models::user::User` does not have 
this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0308]: mismatched types
   --> src\controllers\user_controller.rs:177:28
    |
177 |             date_of_birth: req.date_of_birth.clone(),
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^ 
expected `Option<DateTime<Utc>>`, found `Option<String>`
    |
    = note: expected enum 
`std::option::Option<chrono::DateTime<Utc>>`
               found enum 
`std::option::Option<std::string::String>`

error[E0560]: struct `models::user::User` has no field 
named `country`
   --> src\controllers\user_controller.rs:178:13
    |
178 |             country: req.country.clone(),
    |             ^^^^^^^ `models::user::User` does not 
have this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `state`
   --> src\controllers\user_controller.rs:179:13
    |
179 |             state: req.state.clone(),
    |             ^^^^^ `models::user::User` does not have 
this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `city`
   --> src\controllers\user_controller.rs:180:13
    |
180 |             city: req.city.clone(),
    |             ^^^^ `models::user::User` does not have 
this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `address`
   --> src\controllers\user_controller.rs:181:13
    |
181 |             address: req.address.clone(),
    |             ^^^^^^^ `models::user::User` does not 
have this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `user_is_verified`
   --> src\controllers\user_controller.rs:182:13
    |
182 |             user_is_verified: false,
    |             ^^^^^^^^^^^^^^^^ unknown field
    |
help: a field with a similar name exists
    |
182 -             user_is_verified: false,
182 +             userIsVerified: false,
    |

error[E0308]: mismatched types
   --> src\controllers\user_controller.rs:183:25
    |
183 |             kyc_status: "pending".to_string(),
    |                         ^^^^^^^^^^^^^^^^^^^^^ 
expected `Option<String>`, found `String`
    |
    = note: expected enum 
`std::option::Option<std::string::String>`
             found struct `std::string::String`
help: try wrapping the expression in `Some`
    |
183 |             kyc_status: Some("pending".to_string()),
    |                         +++++                     +

error[E0560]: struct `models::user::User` has no field 
named `referral_code`
   --> src\controllers\user_controller.rs:185:13
    |
185 |             referral_code: req.referral_code.clone(),
    |             ^^^^^^^^^^^^^ `models::user::User` does 
not have this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `profile_picture`
   --> src\controllers\user_controller.rs:186:13
    |
186 |             profile_picture: None,
    |             ^^^^^^^^^^^^^^^ unknown field
    |
help: a field with a similar name exists
    |
186 -             profile_picture: None,
186 +             profilePicture: None,
    |

error[E0560]: struct `models::user::User` has no field 
named `two_factor_enabled`
   --> src\controllers\user_controller.rs:187:13
    |
187 |             two_factor_enabled: false,
    |             ^^^^^^^^^^^^^^^^^^ `models::user::User` 
does not have this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `login_attempts`
   --> src\controllers\user_controller.rs:188:13
    |
188 |             login_attempts: 0,
    |             ^^^^^^^^^^^^^^ `models::user::User` does 
not have this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `account_locked_until`
   --> src\controllers\user_controller.rs:190:13
    |
190 |             account_locked_until: None,
    |             ^^^^^^^^^^^^^^^^^^^^ `models::user::User` 
does not have this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `email_verified`
   --> src\controllers\user_controller.rs:191:13
    |
191 |             email_verified: false,
    |             ^^^^^^^^^^^^^^ `models::user::User` does 
not have this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0560]: struct `models::user::User` has no field 
named `phone_verified`
   --> src\controllers\user_controller.rs:192:13
    |
192 |             phone_verified: false,
    |             ^^^^^^^^^^^^^^ `models::user::User` does 
not have this field
    |
    = note: available fields are: `name`, `phone_number`, 
`status`, `verification_status`, `stripe_customer_id` ... 
and 27 others

error[E0308]: mismatched types
   --> src\controllers\user_controller.rs:193:25
    |
193 |             created_at: now,
    |                         ^^^ expected 
`Option<DateTime<Utc>>`, found `DateTime<Utc>`
    |
    = note: expected enum 
`std::option::Option<chrono::DateTime<_>>`
             found struct `chrono::DateTime<_>`
help: try wrapping the expression in `Some`
    |
193 |             created_at: Some(now),
    |                         +++++   +

error[E0271]: expected 
`{closure@user_controller.rs:198:27}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\user_controller.rs:198:30: 198:35}`
   --> src\controllers\user_controller.rs:198:30
    |
198 |           let insert_user = || async {
    |  ___________________________--_^
    | |                           |
    | |                           this closure
199 | |             data.db.collection::<User>("users")
200 | |                 .insert_one(&user_doc, None)
201 | |                 .await
202 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
203 |
204 |           let insert_result = match 
retry(ExponentialBackoff::default(), insert_user).await {
    |                                     -----             
                   ----------- closure used here
    |                                     |
    |                                     required by a 
bound introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\user_controller.rs:198:30: 198:35}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\user_controller.rs:204:85
    |
204 |         let insert_result = match 
retry(ExponentialBackoff::default(), insert_user).await {
    |                                   
--------------------------------------------------^^^^^
    |                                   |                   
                             ||
    |                                   |                   
                             |`Result<_, 
backoff::Error<_>>` is not a future
    |                                   |                   
                             help: remove the `.await`
    |                                   this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0560]: struct `models::account::Account` has no 
field named `user_id`
   --> src\controllers\user_controller.rs:225:13
    |
225 |             user_id: user_id,
    |             ^^^^^^^ unknown field
    |
help: a field with a similar name exists
    |
225 -             user_id: user_id,
225 +             userId: user_id,
    |

error[E0560]: struct `models::account::Account` has no 
field named `account_number`
   --> src\controllers\user_controller.rs:226:13
    |
226 |             account_number: account_number.clone(),
    |             ^^^^^^^^^^^^^^ unknown field
    |
help: a field with a similar name exists
    |
226 -             account_number: account_number.clone(),
226 +             accountNumber: account_number.clone(),
    |

error[E0560]: struct `models::account::Account` has no 
field named `currency`
   --> src\controllers\user_controller.rs:228:13
    |
228 |             currency: "USD".to_string(),
    |             ^^^^^^^^ `models::account::Account` does 
not have this field
    |
    = note: available fields are: `userId`, 
`accountNumber`, `username`, `countries`, `transactions` 
... and 4 others

error[E0560]: struct `models::account::Account` has no 
field named `is_active`
   --> src\controllers\user_controller.rs:231:13
    |
231 |             is_active: true,
    |             ^^^^^^^^^ `models::account::Account` does 
not have this field
    |
    = note: available fields are: `userId`, 
`accountNumber`, `username`, `countries`, `transactions` 
... and 4 others

error[E0308]: mismatched types
   --> src\controllers\user_controller.rs:232:25
    |
232 |             created_at: now,
    |                         ^^^ expected 
`Option<DateTime<Utc>>`, found `DateTime<Utc>`
    |
    = note: expected enum 
`std::option::Option<chrono::DateTime<_>>`
             found struct `chrono::DateTime<_>`
help: try wrapping the expression in `Some`
    |
232 |             created_at: Some(now),
    |                         +++++   +

error[E0271]: expected 
`{closure@user_controller.rs:237:30}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\user_controller.rs:237:33: 237:38}`
   --> src\controllers\user_controller.rs:237:33
    |
237 |           let insert_account = || async {
    |  ______________________________--_^
    | |                              |
    | |                              this closure
238 | |             
data.db.collection::<Account>("accounts")
239 | |                 .insert_one(&account_doc, None)
240 | |                 .await
241 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
242 |
243 |           if let Err(e) = 
retry(ExponentialBackoff::default(), insert_account).await {
    |                           -----                       
         -------------- closure used here
    |                           |
    |                           required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\user_controller.rs:237:33: 237:38}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\user_controller.rs:243:78
    |
243 |         if let Err(e) = 
retry(ExponentialBackoff::default(), insert_account).await {
    |                         
-----------------------------------------------------^^^^^
    |                         |                             
                      ||
    |                         |                             
                      |`Result<_, backoff::Error<_>>` is 
not a future
    |                         |                             
                      help: remove the `.await`
    |                         this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0599]: no method named `extensions` found for struct 
`HttpRequest` in the current scope
   --> src\controllers\user_controller.rs:306:35
    |
306 |         let extensions = http_req.extensions();
    |                                   ^^^^^^^^^^
    |
   ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\actix-http-3.10.0\src\http_message.rs:29:8
    |
29  |     fn extensions(&self) -> Ref<'_, Extensions>;
    |        ---------- the method is available for 
`HttpRequest` here
    |
    = help: items from traits can only be used if the trait 
is in scope
help: trait `HttpMessage` which provides `extensions` is 
implemented but not in scope; perhaps you want to import it
    |
1   + use actix_web::HttpMessage;
    |
help: there is a method `extensions_mut` with a similar name
    |
306 |         let extensions = http_req.extensions_mut();
    |                                             ++++

error[E0271]: expected 
`{closure@user_controller.rs:337:25}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\user_controller.rs:337:28: 337:33}`
   --> src\controllers\user_controller.rs:337:28
    |
337 |           let find_user = || async {
    |  _________________________--_^
    | |                         |
    | |                         this closure
338 | |             data.db.collection::<User>("users")
339 | |                 .find_one(doc! { "_id": user_oid }, 
None)
340 | |                 .await
341 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
342 |
343 |           let user = match 
retry(ExponentialBackoff::default(), find_user).await {
    |                            -----                      
          --------- closure used here
    |                            |
    |                            required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\user_controller.rs:337:28: 337:33}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\user_controller.rs:343:74
    |
343 |         let user = match 
retry(ExponentialBackoff::default(), find_user).await {
    |                          
------------------------------------------------^^^^^
    |                          |                            
                  ||
    |                          |                            
                  |`Result<_, backoff::Error<_>>` is not a 
future
    |                          |                            
                  help: remove the `.await`
    |                          this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0271]: expected 
`{closure@user_controller.rs:383:29}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\user_controller.rs:383:32: 383:37}`
   --> src\controllers\user_controller.rs:383:32
    |
383 |           let find_accounts = || async {
    |  _____________________________--_^
    | |                             |
    | |                             this closure
384 | |             
data.db.collection::<Account>("accounts")
385 | |                 .find(doc! { "userId": user_oid }, 
None)
386 | |                 .await
387 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
388 |
389 |           let accounts_cursor = match 
retry(ExponentialBackoff::default(), find_accounts).await {
    |                                       -----           
                     ------------- closure used here
    |                                       |
    |                                       required by a 
bound introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\user_controller.rs:383:32: 383:37}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\user_controller.rs:389:89
    |
389 |         let accounts_cursor = match 
retry(ExponentialBackoff::default(), find_accounts).await {
    |                                     
----------------------------------------------------^^^^^
    |                                     |                 
                                 ||
    |                                     |                 
                                 |`Result<_, 
backoff::Error<_>>` is not a future
    |                                     |                 
                                 help: remove the `.await`
    |                                     this call returns 
`Result<_, backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0609]: no field `account_number` on type 
`&models::account::Account`
   --> src\controllers\user_controller.rs:434:42
    |
434 |                     "accountNumber": 
acc.account_number,
    |                                          
^^^^^^^^^^^^^^ unknown field
    |
help: a field with a similar name exists
    |
434 -                     "accountNumber": 
acc.account_number,
434 +                     "accountNumber": 
acc.accountNumber,
    |

error[E0609]: no field `currency` on type 
`&models::account::Account`
   --> src\controllers\user_controller.rs:436:37
    |
436 |                     "currency": acc.currency,
    |                                     ^^^^^^^^ unknown 
field
    |
    = note: available fields are: `id`, `userId`, 
`accountNumber`, `account_type`, `username` ... and 10 
others

error[E0599]: no method named `extensions` found for struct 
`HttpRequest` in the current scope
   --> src\controllers\user_controller.rs:456:35
    |
456 |         let extensions = http_req.extensions();
    |                                   ^^^^^^^^^^
    |
   ::: C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\actix-http-3.10.0\src\http_message.rs:29:8
    |
29  |     fn extensions(&self) -> Ref<'_, Extensions>;
    |        ---------- the method is available for 
`HttpRequest` here
    |
    = help: items from traits can only be used if the trait 
is in scope
help: trait `HttpMessage` which provides `extensions` is 
implemented but not in scope; perhaps you want to import it
    |
1   + use actix_web::HttpMessage;
    |
help: there is a method `extensions_mut` with a similar name
    |
456 |         let extensions = http_req.extensions_mut();
    |                                             ++++

error[E0271]: expected 
`{closure@user_controller.rs:487:25}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\user_controller.rs:487:28: 487:33}`
   --> src\controllers\user_controller.rs:487:28
    |
487 |           let find_user = || async {
    |  _________________________--_^
    | |                         |
    | |                         this closure
488 | |             data.db.collection::<User>("users")
489 | |                 .find_one(doc! { "_id": user_oid }, 
None)
490 | |                 .await
491 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
492 |
493 |           let user = match 
retry(ExponentialBackoff::default(), find_user).await {
    |                            -----                      
          --------- closure used here
    |                            |
    |                            required by a bound 
introduced by this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\user_controller.rs:487:28: 487:33}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\user_controller.rs:493:74
    |
493 |         let user = match 
retry(ExponentialBackoff::default(), find_user).await {
    |                          
------------------------------------------------^^^^^
    |                          |                            
                  ||
    |                          |                            
                  |`Result<_, backoff::Error<_>>` is not a 
future
    |                          |                            
                  help: remove the `.await`
    |                          this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0277]: the trait bound `UpdateModifications: 
std::convert::From<&bson::Document>` is not satisfied
   --> src\controllers\user_controller.rs:582:55
    |
582 |                 .update_one(doc! { "_id": user_oid }, 
&update_doc, None)
    |                  ----------                           
^^^^^^^^^^^ the trait `std::convert::From<&bson::Document>` 
is not implemented for `UpdateModifications`
    |                  |
    |                  required by a bound introduced by 
this call
    |
    = help: the following other types implement trait 
`std::convert::From<T>`:
              `UpdateModifications` implements 
`std::convert::From<Vec<bson::Document>>`
              `UpdateModifications` implements 
`std::convert::From<bson::Document>`
    = note: required for `&bson::Document` to implement 
`Into<UpdateModifications>`
note: required by a bound in 
`mongodb::Collection::<T>::update_one`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\mongodb-2.8.2\src\coll.rs:850:22
    |
847 |     pub async fn update_one(
    |                  ---------- required by a bound in 
this associated function
...
850 |         update: impl Into<UpdateModifications>,
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^ 
required by this bound in `Collection::<T>::update_one`

error[E0277]: the trait bound `UpdateModifications: 
std::convert::From<&bson::Document>` is not satisfied
   --> src\controllers\user_controller.rs:581:13
    |
581 | /             data.db.collection::<User>("users")
582 | |                 .update_one(doc! { "_id": user_oid 
}, &update_doc, None)
    | |_____________________________________________________
___________________^ the trait 
`std::convert::From<&bson::Document>` is not implemented 
for `UpdateModifications`
    |
    = help: the following other types implement trait 
`std::convert::From<T>`:
              `UpdateModifications` implements 
`std::convert::From<Vec<bson::Document>>`
              `UpdateModifications` implements 
`std::convert::From<bson::Document>`
    = note: required for `&bson::Document` to implement 
`Into<UpdateModifications>`
note: required by a bound in 
`mongodb::Collection::<T>::update_one`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\mongodb-2.8.2\src\coll.rs:850:22
    |
847 |     pub async fn update_one(
    |                  ---------- required by a bound in 
this associated function
...
850 |         update: impl Into<UpdateModifications>,
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^ 
required by this bound in `Collection::<T>::update_one`

error[E0277]: the trait bound `UpdateModifications: 
std::convert::From<&bson::Document>` is not satisfied
   --> src\controllers\user_controller.rs:583:18
    |
583 |                 .await
    |                  ^^^^^ the trait 
`std::convert::From<&bson::Document>` is not implemented 
for `UpdateModifications`
    |
    = help: the following other types implement trait 
`std::convert::From<T>`:
              `UpdateModifications` implements 
`std::convert::From<Vec<bson::Document>>`
              `UpdateModifications` implements 
`std::convert::From<bson::Document>`
    = note: required for `&bson::Document` to implement 
`Into<UpdateModifications>`
note: required by a bound in 
`mongodb::Collection::<T>::update_one`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\mongodb-2.8.2\src\coll.rs:850:22
    |
847 |     pub async fn update_one(
    |                  ---------- required by a bound in 
this associated function
...
850 |         update: impl Into<UpdateModifications>,
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^ 
required by this bound in `Collection::<T>::update_one`

error[E0271]: expected 
`{closure@user_controller.rs:580:27}` to return `Result<_, 
Error<_>>`, but it returns `{async 
block@src\controllers\user_controller.rs:580:30: 580:35}`
   --> src\controllers\user_controller.rs:580:30
    |
580 |           let update_user = || async {
    |  ___________________________--_^
    | |                           |
    | |                           this closure
581 | |             data.db.collection::<User>("users")
582 | |                 .update_one(doc! { "_id": user_oid 
}, &update_doc, None)
583 | |                 .await
584 | |         };
    | |_________^ expected `Result<_, Error<_>>`, found 
`async` block
585 |
586 |           match retry(ExponentialBackoff::default(), 
update_user).await {
    |                 -----                                
----------- closure used here
    |                 |
    |                 required by a bound introduced by 
this call
    |
    = note:       expected enum `Result<_, 
backoff::Error<_>>`
            found `async` block `{async 
block@src\controllers\user_controller.rs:580:30: 580:35}`
note: required by a bound in `backoff::retry`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\backoff-0.4.0\src\retry.rs:25:19
    |
23  | pub fn retry<F, B, T, E>(backoff: B, op: F) -> 
Result<T, Error<E>>
    |        ----- required by a bound in this function
24  | where
25  |     F: FnMut() -> Result<T, Error<E>>,
    |                   ^^^^^^^^^^^^^^^^^^^ required by 
this bound in `retry`

error[E0277]: `Result<_, backoff::Error<_>>` is not a future
   --> src\controllers\user_controller.rs:586:65
    |
586 |         match retry(ExponentialBackoff::default(), 
update_user).await {
    |               
--------------------------------------------------^^^^^
    |               |                                       
         ||
    |               |                                       
         |`Result<_, backoff::Error<_>>` is not a future
    |               |                                       
         help: remove the `.await`
    |               this call returns `Result<_, 
backoff::Error<_>>`
    |
    = help: the trait `futures_util::Future` is not 
implemented for `Result<_, backoff::Error<_>>`
    = note: Result<_, backoff::Error<_>> must be a future 
or must implement `IntoFuture` to be awaited
    = note: required for `Result<_, backoff::Error<_>>` to 
implement `std::future::IntoFuture`

error[E0308]: mismatched types
   --> src\controllers\withdrawal.rs:41:24
    |
41  |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |                    |
    |                    arguments to this enum variant are 
incorrect
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\withdrawal.rs:41:20
    |
41  |             return Err(ResponseHelper::validation_erro
r(&validation_errors.to_string()));
    |                    ^^^^-------------------------------
---------------------------------^
    |                        |
    |                        this argument influences the 
type of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0277]: `?` couldn't convert the error to 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
  --> src\controllers\withdrawal.rs:51:15
   |
44 |           let user_id = 
ObjectId::parse_str(&claims.sub)
   |                         
-------------------------------- this can't be annotated 
with `?` because it has type `Result<_, bson::oid::Error>`
45 |               .map_err(|_| {
   |  ______________-
46 | |                 ResponseHelper::bad_request(
47 | |                     messages::INVALID_USER_ID,
48 | |                     "Invalid user ID format",
...  |
51 | |             })?;
   | |              -^ unsatisfied trait bound
   | |______________|
   |                this can't be annotated with `?` 
because it has type `Result<_, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>`
   |
   = help: the trait 
`std::convert::From<(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>` is not implemented for 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
   = note: the question mark operation (`?`) implicitly 
performs a conversion on the error value using the `From` 
trait
   = help: the following other types implement trait 
`std::convert::From<T>`:
             `(T, T)` implements `std::convert::From<[T; 
2]>`
             `(T, T, T)` implements `std::convert::From<[T; 
3]>`
             `(T, T, T, T)` implements 
`std::convert::From<[T; 4]>`
             `(T, T, T, T, T)` implements 
`std::convert::From<[T; 5]>`
             `(T, T, T, T, T, T)` implements 
`std::convert::From<[T; 6]>`
             `(T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 7]>`
             `(T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 8]>`
             `(T, T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 9]>`
           and 6 others
   = note: required for `Result<Json<PayoutResponse>, 
(StatusCode, Json<Value>)>` to implement 
`FromResidual<Result<Infallible, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>>`
   = note: the full name for the type has been written to 'D
:\workspace\.upwork\sangapay\sangapay-rust\target\debug\deps
\sangapay-89f53a53efa19b7f.long-type-12950418791385501243.tx
t'
   = note: consider using `--verbose` to print the full 
type name to the console

error[E0308]: mismatched types
   --> src\controllers\withdrawal.rs:64:21
    |
64  |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
65  | |                     messages::INTERNAL_SERVER_ERROR,
66  | |                     &e.to_string(),
67  | |                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
68  | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\withdrawal.rs:64:17
    |
64  |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
65  | ||                     
messages::INTERNAL_SERVER_ERROR,
66  | ||                     &e.to_string(),
67  | ||                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
68  | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0277]: `?` couldn't convert the error to 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
  --> src\controllers\withdrawal.rs:86:15
   |
79 |           let user_id = 
ObjectId::parse_str(&claims.sub)
   |                         
-------------------------------- this can't be annotated 
with `?` because it has type `Result<_, bson::oid::Error>`
80 |               .map_err(|_| {
   |  ______________-
81 | |                 ResponseHelper::bad_request(
82 | |                     messages::INVALID_USER_ID,
83 | |                     "Invalid user ID format",
...  |
86 | |             })?;
   | |              -^ unsatisfied trait bound
   | |______________|
   |                this can't be annotated with `?` 
because it has type `Result<_, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>`
   |
   = help: the trait 
`std::convert::From<(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>` is not implemented for 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
   = note: the question mark operation (`?`) implicitly 
performs a conversion on the error value using the `From` 
trait
   = help: the following other types implement trait 
`std::convert::From<T>`:
             `(T, T)` implements `std::convert::From<[T; 
2]>`
             `(T, T, T)` implements `std::convert::From<[T; 
3]>`
             `(T, T, T, T)` implements 
`std::convert::From<[T; 4]>`
             `(T, T, T, T, T)` implements 
`std::convert::From<[T; 5]>`
             `(T, T, T, T, T, T)` implements 
`std::convert::From<[T; 6]>`
             `(T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 7]>`
             `(T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 8]>`
             `(T, T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 9]>`
           and 6 others
   = note: required for `Result<Json<Value>, (StatusCode, 
Json<Value>)>` to implement 
`FromResidual<Result<Infallible, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>>`
   = note: the full name for the type has been written to 'D
:\workspace\.upwork\sangapay\sangapay-rust\target\debug\deps
\sangapay-89f53a53efa19b7f.long-type-15413517677997627361.tx
t'
   = note: consider using `--verbose` to print the full 
type name to the console

error[E0308]: mismatched types
   --> src\controllers\withdrawal.rs:95:21
    |
95  |                   Err(ResponseHelper::not_found(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
96  | |                     messages::PAYOUT_NOT_FOUND,
97  | |                     "Payout not found for this 
user",
98  | |                     
vec![suggestions::CHECK_INPUT_DATA]
99  | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\withdrawal.rs:95:17
    |
95  |                    Err(ResponseHelper::not_found(
    |   _________________^   -
    |  |_____________________|
96  | ||                     messages::PAYOUT_NOT_FOUND,
97  | ||                     "Payout not found for this 
user",
98  | ||                     
vec![suggestions::CHECK_INPUT_DATA]
99  | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0308]: mismatched types
   --> src\controllers\withdrawal.rs:103:21
    |
103 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
104 | |                     messages::INTERNAL_SERVER_ERROR,
105 | |                     &e.to_string(),
106 | |                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
107 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\withdrawal.rs:103:17
    |
103 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
104 | ||                     
messages::INTERNAL_SERVER_ERROR,
105 | ||                     &e.to_string(),
106 | ||                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
107 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0277]: `?` couldn't convert the error to 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
   --> src\controllers\withdrawal.rs:125:15
    |
118 |           let user_id = 
ObjectId::parse_str(&claims.sub)
    |                         
-------------------------------- this can't be annotated 
with `?` because it has type `Result<_, bson::oid::Error>`
119 |               .map_err(|_| {
    |  ______________-
120 | |                 ResponseHelper::bad_request(
121 | |                     messages::INVALID_USER_ID,
122 | |                     "Invalid user ID format",
...   |
125 | |             })?;
    | |              -^ unsatisfied trait bound
    | |______________|
    |                this can't be annotated with `?` 
because it has type `Result<_, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>`
    |
    = help: the trait 
`std::convert::From<(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>` is not implemented for 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
    = note: the question mark operation (`?`) implicitly 
performs a conversion on the error value using the `From` 
trait
    = help: the following other types implement trait 
`std::convert::From<T>`:
              `(T, T)` implements `std::convert::From<[T; 
2]>`
              `(T, T, T)` implements 
`std::convert::From<[T; 3]>`
              `(T, T, T, T)` implements 
`std::convert::From<[T; 4]>`
              `(T, T, T, T, T)` implements 
`std::convert::From<[T; 5]>`
              `(T, T, T, T, T, T)` implements 
`std::convert::From<[T; 6]>`
              `(T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 7]>`
              `(T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 8]>`
              `(T, T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 9]>`
            and 6 others
    = note: required for `Result<Json<Value>, (StatusCode, 
Json<Value>)>` to implement 
`FromResidual<Result<Infallible, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>>`
    = note: the full name for the type has been written to '
D:\workspace\.upwork\sangapay\sangapay-rust\target\debug\dep
s\sangapay-89f53a53efa19b7f.long-type-15413517677997627361.t
xt'
    = note: consider using `--verbose` to print the full 
type name to the console

error[E0308]: mismatched types
   --> src\controllers\withdrawal.rs:153:21
    |
153 |                   Err(ResponseHelper::internal_error(
    |  _________________---_^
    | |                 |
    | |                 arguments to this enum variant are 
incorrect
154 | |                     messages::INTERNAL_SERVER_ERROR,
155 | |                     &e.to_string(),
156 | |                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
157 | |                 ))
    | |_________________^ expected `(StatusCode, 
Json<Value>)`, found `(StatusCode, Json<ApiResponse<()>>)`
    |
    = note: expected tuple `(axum::http::StatusCode, 
axum::Json<serde_json::Value>)`
               found tuple `(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)`
help: the type constructed contains 
`(axum::http::StatusCode, axum::Json<ApiResponse<()>>)` due 
to the type of the argument passed
   --> src\controllers\withdrawal.rs:153:17
    |
153 |                    Err(ResponseHelper::internal_error(
    |   _________________^   -
    |  |_____________________|
154 | ||                     
messages::INTERNAL_SERVER_ERROR,
155 | ||                     &e.to_string(),
156 | ||                     
vec![suggestions::TRY_AGAIN_LATER, 
suggestions::CONTACT_SUPPORT]
157 | ||                 ))
    | ||_________________-^
    | |__________________|
    |                    this argument influences the type 
of `Err`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:537
:5
    |
537 |     Err(#[stable(feature = "rust1", since = "1.0.0")] 
E),
    |     ^^^

error[E0277]: `?` couldn't convert the error to 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
   --> src\controllers\withdrawal.rs:178:15
    |
172 |               .ok_or_else(|| {
    |  ______________-
173 | |                 ResponseHelper::bad_request(
174 | |                     
messages::MISSING_STRIPE_SIGNATURE,
175 | |                     "Stripe signature header is 
required",
...   |
178 | |             })?;
    | |              -^ unsatisfied trait bound
    | |______________|
    |                this can't be annotated with `?` 
because it has type `Result<_, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>`
    |
    = help: the trait 
`std::convert::From<(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>` is not implemented for 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
    = note: the question mark operation (`?`) implicitly 
performs a conversion on the error value using the `From` 
trait
    = help: the following other types implement trait 
`std::convert::From<T>`:
              `(T, T)` implements `std::convert::From<[T; 
2]>`
              `(T, T, T)` implements 
`std::convert::From<[T; 3]>`
              `(T, T, T, T)` implements 
`std::convert::From<[T; 4]>`
              `(T, T, T, T, T)` implements 
`std::convert::From<[T; 5]>`
              `(T, T, T, T, T, T)` implements 
`std::convert::From<[T; 6]>`
              `(T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 7]>`
              `(T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 8]>`
              `(T, T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 9]>`
            and 6 others
    = note: required for 
`Result<Json<WebhookCallbackResponse>, (StatusCode, 
Json<Value>)>` to implement 
`FromResidual<Result<Infallible, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>>`
    = note: the full name for the type has been written to '
D:\workspace\.upwork\sangapay\sangapay-rust\target\debug\dep
s\sangapay-89f53a53efa19b7f.long-type-14942391310499267503.t
xt'
    = note: consider using `--verbose` to print the full 
type name to the console

error[E0277]: `?` couldn't convert the error to 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
   --> src\controllers\withdrawal.rs:197:15
    |
190 |           let webhook_data: serde_json::Value = 
serde_json::from_slice(&body_bytes)
    |                                                 
----------------------------------- this can't be annotated 
with `?` because it has type `Result<_, serde_json::Error>`
191 |               .map_err(|_| {
    |  ______________-
192 | |                 ResponseHelper::bad_request(
193 | |                     messages::INVALID_JSON,
194 | |                     "Invalid JSON in request body",
...   |
197 | |             })?;
    | |              -^ unsatisfied trait bound
    | |______________|
    |                this can't be annotated with `?` 
because it has type `Result<_, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>`
    |
    = help: the trait 
`std::convert::From<(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>` is not implemented for 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
    = note: the question mark operation (`?`) implicitly 
performs a conversion on the error value using the `From` 
trait
    = help: the following other types implement trait 
`std::convert::From<T>`:
              `(T, T)` implements `std::convert::From<[T; 
2]>`
              `(T, T, T)` implements 
`std::convert::From<[T; 3]>`
              `(T, T, T, T)` implements 
`std::convert::From<[T; 4]>`
              `(T, T, T, T, T)` implements 
`std::convert::From<[T; 5]>`
              `(T, T, T, T, T, T)` implements 
`std::convert::From<[T; 6]>`
              `(T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 7]>`
              `(T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 8]>`
              `(T, T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 9]>`
            and 6 others
    = note: required for 
`Result<Json<WebhookCallbackResponse>, (StatusCode, 
Json<Value>)>` to implement 
`FromResidual<Result<Infallible, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>>`
    = note: the full name for the type has been written to '
D:\workspace\.upwork\sangapay\sangapay-rust\target\debug\dep
s\sangapay-89f53a53efa19b7f.long-type-14942391310499267503.t
xt'
    = note: consider using `--verbose` to print the full 
type name to the console

error[E0277]: `?` couldn't convert the error to 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
   --> src\controllers\withdrawal.rs:208:15
    |
202 |               .ok_or_else(|| {
    |  ______________-
203 | |                 ResponseHelper::bad_request(
204 | |                     messages::MISSING_EVENT_TYPE,
205 | |                     "Event type is required in 
webhook data",
...   |
208 | |             })?;
    | |              -^ unsatisfied trait bound
    | |______________|
    |                this can't be annotated with `?` 
because it has type `Result<_, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>`
    |
    = help: the trait 
`std::convert::From<(axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>` is not implemented for 
`(axum::http::StatusCode, axum::Json<serde_json::Value>)`
    = note: the question mark operation (`?`) implicitly 
performs a conversion on the error value using the `From` 
trait
    = help: the following other types implement trait 
`std::convert::From<T>`:
              `(T, T)` implements `std::convert::From<[T; 
2]>`
              `(T, T, T)` implements 
`std::convert::From<[T; 3]>`
              `(T, T, T, T)` implements 
`std::convert::From<[T; 4]>`
              `(T, T, T, T, T)` implements 
`std::convert::From<[T; 5]>`
              `(T, T, T, T, T, T)` implements 
`std::convert::From<[T; 6]>`
              `(T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 7]>`
              `(T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 8]>`
              `(T, T, T, T, T, T, T, T, T)` implements 
`std::convert::From<[T; 9]>`
            and 6 others
    = note: required for 
`Result<Json<WebhookCallbackResponse>, (StatusCode, 
Json<Value>)>` to implement 
`FromResidual<Result<Infallible, (axum::http::StatusCode, 
axum::Json<ApiResponse<()>>)>>`
    = note: the full name for the type has been written to '
D:\workspace\.upwork\sangapay\sangapay-rust\target\debug\dep
s\sangapay-89f53a53efa19b7f.long-type-14942391310499267503.t
xt'
    = note: consider using `--verbose` to print the full 
type name to the console

error[E0616]: field `db` of struct `WithdrawalService` is 
private
   --> src\controllers\withdrawal.rs:238:50
    |
238 |         let collection = self.withdrawal_service.db.co
llection::<crate::models::payout::Payout>("payouts");
    |                                                  ^^ 
private field

error[E0616]: field `db` of struct `WithdrawalService` is 
private
   --> src\controllers\withdrawal.rs:258:50
    |
258 |         let collection = self.withdrawal_service.db.co
llection::<crate::models::payout::Payout>("payouts");
    |                                                  ^^ 
private field

warning: unused variable: `payload`
   --> src\middleware\security.rs:165:5
    |
165 |     payload: web::Payload,
    |     ^^^^^^^ help: if this is intentional, prefix it 
with an underscore: `_payload`

warning: unused variable: `payload`
   --> src\middleware\auth.rs:222:5
    |
222 |     payload: web::Payload,
    |     ^^^^^^^ help: if this is intentional, prefix it 
with an underscore: `_payload`

warning: unused variable: `payload`
   --> src\middleware\auth.rs:231:5
    |
231 |     payload: web::Payload,
    |     ^^^^^^^ help: if this is intentional, prefix it 
with an underscore: `_payload`

error[E0063]: missing fields `countries`, `creditCard`, 
`pin` and 4 other fields in initializer of 
`models::account::Account`
   --> src\services\account_number.rs:159:23
    |
159 |         let account = Account {
    |                       ^^^^^^^ missing `countries`, 
`creditCard`, `pin` and 4 other fields

error[E0308]: mismatched types
   --> src\services\exchange.rs:119:53
    |
119 |                       return 
Err(reqwest::Error::from(std::io::Error::new(
    |  
________________________________--------------------_^
    | |                                |
    | |                                arguments to this 
function are incorrect
120 | |                         std::io::ErrorKind::Other,
121 | |                         "Failed to fetch latest 
rates"
122 | |                     )));
    | |_____________________^ expected `reqwest::Error`, 
found `std::io::Error`
    |
    = note: `std::io::Error` and `reqwest::Error` have 
similar names, but are actually distinct types
note: `std::io::Error` is defined in crate `std`
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\std\src\io\error.rs:64
:1
    |
64  | pub struct Error {
    | ^^^^^^^^^^^^^^^^
note: `reqwest::Error` is defined in crate `reqwest`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\reqwest-0.11.27\src\error.rs:16:1
    |
16  | pub struct Error {
    | ^^^^^^^^^^^^^^^^
note: associated function defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\convert\mod.r
s:587:8
    |
587 |     fn from(value: T) -> Self;
    |        ^^^^

error[E0308]: mismatched types
   --> src\services\exchange.rs:148:42
    |
148 |                   
Err(reqwest::Error::from(std::io::Error::new(
    |  _____________________--------------------_^
    | |                     |
    | |                     arguments to this function are 
incorrect
149 | |                     std::io::ErrorKind::Other,
150 | |                     format!("Exchange rate API 
error: {}", error_text)
151 | |                 )))
    | |_________________^ expected `reqwest::Error`, found 
`std::io::Error`
    |
    = note: `std::io::Error` and `reqwest::Error` have 
similar names, but are actually distinct types
note: `std::io::Error` is defined in crate `std`
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\std\src\io\error.rs:64
:1
    |
64  | pub struct Error {
    | ^^^^^^^^^^^^^^^^
note: `reqwest::Error` is defined in crate `reqwest`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\reqwest-0.11.27\src\error.rs:16:1
    |
16  | pub struct Error {
    | ^^^^^^^^^^^^^^^^
note: associated function defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\convert\mod.r
s:587:8
    |
587 |     fn from(value: T) -> Self;
    |        ^^^^

error[E0308]: mismatched types
   --> src\services\exchange.rs:351:53
    |
351 |                       return 
Err(reqwest::Error::from(std::io::Error::new(
    |  
________________________________--------------------_^
    | |                                |
    | |                                arguments to this 
function are incorrect
352 | |                         std::io::ErrorKind::Other,
353 | |                         "Failed to fetch historical 
rates"
354 | |                     )));
    | |_____________________^ expected `reqwest::Error`, 
found `std::io::Error`
    |
    = note: `std::io::Error` and `reqwest::Error` have 
similar names, but are actually distinct types
note: `std::io::Error` is defined in crate `std`
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\std\src\io\error.rs:64
:1
    |
64  | pub struct Error {
    | ^^^^^^^^^^^^^^^^
note: `reqwest::Error` is defined in crate `reqwest`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\reqwest-0.11.27\src\error.rs:16:1
    |
16  | pub struct Error {
    | ^^^^^^^^^^^^^^^^
note: associated function defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\convert\mod.r
s:587:8
    |
587 |     fn from(value: T) -> Self;
    |        ^^^^

error[E0308]: mismatched types
   --> src\services\exchange.rs:360:42
    |
360 |                   
Err(reqwest::Error::from(std::io::Error::new(
    |  _____________________--------------------_^
    | |                     |
    | |                     arguments to this function are 
incorrect
361 | |                     std::io::ErrorKind::Other,
362 | |                     format!("Historical rates API 
error: {}", error_text)
363 | |                 )))
    | |_________________^ expected `reqwest::Error`, found 
`std::io::Error`
    |
    = note: `std::io::Error` and `reqwest::Error` have 
similar names, but are actually distinct types
note: `std::io::Error` is defined in crate `std`
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\std\src\io\error.rs:64
:1
    |
64  | pub struct Error {
    | ^^^^^^^^^^^^^^^^
note: `reqwest::Error` is defined in crate `reqwest`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-194
9cf8c6b5b557f\reqwest-0.11.27\src\error.rs:16:1
    |
16  | pub struct Error {
    | ^^^^^^^^^^^^^^^^
note: associated function defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-win
dows-gnu\lib/rustlib/src/rust\library\core\src\convert\mod.r
s:587:8
    |
587 |     fn from(value: T) -> Self;
    |        ^^^^

error[E0599]: no method named `verify_grant_token` found 
for struct `TokenService` in the current scope
   --> src\services\oauth.rs:243:48
    |
243 |         let decoded_token = 
self.token_service.verify_grant_token(refresh_token, 
"oauth_refresh")?;
    |                                                
^^^^^^^^^^^^^^^^^^
    |
   ::: src\services\token.rs:55:1
    |
55  | pub struct TokenService {
    | ----------------------- method `verify_grant_token` 
not found for this struct
    |
help: there is a method `generate_grant_token` with a 
similar name, but with different arguments
   --> src\services\token.rs:113:5
    |
113 | /     pub fn generate_grant_token(
114 | |         &self,
115 | |         payload: serde_json::Value,
116 | |         expires_in: i64,
...   |
119 | |         token_type: TokenType,
120 | |     ) -> Result<String> {
    | |_______________________^

error[E0599]: no method named `verify_token` found for 
struct `TokenService` in the current scope
   --> src\services\oauth.rs:325:34
    |
325 |         match self.token_service.verify_token(token) {
    |                                  ^^^^^^^^^^^^ method 
not found in `TokenService`
    |
   ::: src\services\token.rs:55:1
    |
55  | pub struct TokenService {
    | ----------------------- method `verify_token` not 
found for this struct

error[E0308]: mismatched types
    --> src\services\stripe.rs:1519:25
     |
1519 |                     id: format!("line_item_{}", 
uuid::Uuid::new_v4()),
     |                         
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected 
`TaxCalculationLineItemId`, found `String`
     |
     = note: this error originates in the macro `format` 
(in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0560]: struct `TaxCalculationLineItem` has no field 
named `object`
    --> src\services\stripe.rs:1521:21
     |
1521 |                     object: 
"tax.calculation_line_item".to_string(),
     |                     ^^^^^^ `TaxCalculationLineItem` 
does not have this field
     |
     = note: all struct fields are already assigned

error[E0308]: mismatched types
    --> src\services\stripe.rs:1575:21
     |
1575 |                 id: format!("taxcalc_{}", 
uuid::Uuid::new_v4()),
     |                     
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected 
`TaxCalculationId`, found `String`
     |
note: return type inferred to be `TaxCalculationId` here
    --> src\services\stripe.rs:1514:31
     |
1514 |             params.currency = currency.parse()?;
     |                               ^^^^^^^^^^^^^^^^^
     = note: this error originates in the macro `format` 
(in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0560]: struct `TaxCalculation` has no field named 
`object`
    --> src\services\stripe.rs:1576:17
     |
1576 |                 object: 
"tax.calculation".to_string(),
     |                 ^^^^^^ `TaxCalculation` does not 
have this field
     |
     = note: available fields are: `customer`

error[E0599]: no method named `len` found for struct 
`stripe::List` in the current scope
    --> src\services\stripe.rs:1616:89
     |
1616 |             info!("Tax calculation completed for {} 
line items", calculation.line_items.len());
     |                                                      
                                   ^^^ method not found in 
`List<TaxCalculationLineItem>`
     |
help: some of the expressions' fields have a method of the 
same name
     |
1616 |             info!("Tax calculation completed for {} 
line items", calculation.line_items.data.len());
     |                                                      
                                   +++++
1616 |             info!("Tax calculation completed for {} 
line items", calculation.line_items.url.len());
     |                                                      
                                   ++++

warning: this function depends on never type fallback being 
`()`
    --> src\services\stripe.rs:1751:5
     |
1751 |     pub async fn list_climate_products(&self, limit: 
Option<u64>) -> Result<List<ClimateProduct>> {
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = warning: this was previously accepted by the 
compiler but is being phased out; it will become a hard 
error in Rust 2024 and in a future release in all editions!
     = note: for more information, see <https://doc.rust-lan
g.org/nightly/edition-guide/rust-2024/never-type-fallback.ht
ml>
     = help: specify the types explicitly
note: in edition 2024, the requirement `!: 
config::_::_serde::Serialize` will fail
    --> src\services\stripe.rs:1754:14
     |
1754 |         self.execute_api_call(Some(cache_key), || 
async {
     |              ^^^^^^^^^^^^^^^^
     = note: 
`#[warn(dependency_on_unit_never_type_fallback)]` on by 
default
help: use `()` annotations to avoid fallback changes
     |
1754 |         self.execute_api_call::<(), _, 
_>(Some(cache_key), || async {
     |                              ++++++++++++

warning: this function depends on never type fallback being 
`()`
    --> src\services\stripe.rs:1764:5
     |
1764 |     pub async fn list_climate_suppliers(&self, 
limit: Option<u64>) -> Result<List<ClimateSupplier>> {
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = warning: this was previously accepted by the 
compiler but is being phased out; it will become a hard 
error in Rust 2024 and in a future release in all editions!
     = note: for more information, see <https://doc.rust-lan
g.org/nightly/edition-guide/rust-2024/never-type-fallback.ht
ml>
     = help: specify the types explicitly
note: in edition 2024, the requirement `!: 
config::_::_serde::Serialize` will fail
    --> src\services\stripe.rs:1767:14
     |
1767 |         self.execute_api_call(Some(cache_key), || 
async {
     |              ^^^^^^^^^^^^^^^^
help: use `()` annotations to avoid fallback changes
     |
1767 |         self.execute_api_call::<(), _, 
_>(Some(cache_key), || async {
     |                              ++++++++++++

warning: this function depends on never type fallback being 
`()`
    --> src\services\stripe.rs:1870:5
     |
1870 |     pub async fn list_report_types(&self) -> 
Result<List<ReportType>> {
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^
     |
     = warning: this was previously accepted by the 
compiler but is being phased out; it will become a hard 
error in Rust 2024 and in a future release in all editions!
     = note: for more information, see <https://doc.rust-lan
g.org/nightly/edition-guide/rust-2024/never-type-fallback.ht
ml>
     = help: specify the types explicitly
note: in edition 2024, the requirement `!: 
config::_::_serde::Serialize` will fail
    --> src\services\stripe.rs:1873:14
     |
1873 |         self.execute_api_call(Some(cache_key), || 
async {
     |              ^^^^^^^^^^^^^^^^
help: use `()` annotations to avoid fallback changes
     |
1873 |         self.execute_api_call::<(), _, 
_>(Some(cache_key), || async {
     |                              ++++++++++++

warning: this function depends on never type fallback being 
`()`
    --> src\services\stripe.rs:1881:5
     |
1881 |     pub async fn 
list_sigma_scheduled_query_runs(&self, limit: Option<u64>) 
-> Result<List<SigmaScheduledQueryRun>> {
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
^^
     |
     = warning: this was previously accepted by the 
compiler but is being phased out; it will become a hard 
error in Rust 2024 and in a future release in all editions!
     = note: for more information, see <https://doc.rust-lan
g.org/nightly/edition-guide/rust-2024/never-type-fallback.ht
ml>
     = help: specify the types explicitly
note: in edition 2024, the requirement `!: 
config::_::_serde::Serialize` will fail
    --> src\services\stripe.rs:1884:14
     |
1884 |         self.execute_api_call(Some(cache_key), || 
async {
     |              ^^^^^^^^^^^^^^^^
help: use `()` annotations to avoid fallback changes
     |
1884 |         self.execute_api_call::<(), _, 
_>(Some(cache_key), || async {
     |                              ++++++++++++

error[E0599]: no function or associated item named `create` 
found for struct `stripe::ApplicationFeeRefund` in the 
current scope
    --> src\services\stripe.rs:2178:48
     |
2178 |             let refund = 
ApplicationFeeRefund::create(&self.client, 
&fee_id.parse()?, params).await?;
     |                                                
^^^^^^ function or associated item not found in 
`ApplicationFeeRefund`

error[E0282]: type annotations needed for `Vec<_>`
    --> src\services\stripe.rs:2262:13
     |
2262 |         let mut handles = Vec::new();
     |             ^^^^^^^^^^^   ---------- type must be 
known at this point
     |
help: consider giving `handles` an explicit type, where the 
type for type parameter `T` is specified
     |
2262 |         let mut handles: Vec<T> = Vec::new();
     |                        ++++++++

error[E0599]: no variant or associated item named `create` 
found for enum `ExternalAccount` in the current scope
    --> src\services\stripe.rs:2394:57
     |
2394 |         let external_account = 
stripe::ExternalAccount::create(
     |                                                      
   ^^^^^^ variant or associated item not found in 
`ExternalAccount`

error[E0599]: no variant or associated item named 
`Physical` found for enum `stripe::CardType` in the current 
scope
    --> src\services\stripe.rs:2453:41
     |
2453 |                 "physical" => CardType::Physical,
     |                                         ^^^^^^^^ 
variant or associated item not found in `CardType`

error[E0599]: no variant or associated item named `Virtual` 
found for enum `stripe::CardType` in the current scope
    --> src\services\stripe.rs:2454:32
     |
2454 |                 _ => CardType::Virtual,
     |                                ^^^^^^^ variant or 
associated item not found in `CardType`

error[E0599]: no function or associated item named `create` 
found for struct `stripe::Card` in the current scope
    --> src\services\stripe.rs:2509:26
     |
2509 |         let card = Card::create(&self.client, 
params).await?;
     |                          ^^^^^^ function or 
associated item not found in `Card`

error[E0599]: no function or associated item named 
`retrieve` found for struct `stripe::Card` in the current 
scope
    --> src\services\stripe.rs:2519:26
     |
2519 |         let card = Card::retrieve(&self.client, 
&card_id.parse()?, Some(params)).await?;
     |                          ^^^^^^^^ function or 
associated item not found in `Card`

error[E0308]: mismatched types
    --> src\services\stripe.rs:2531:32
     |
2531 |                     exp_month: 
card_details.exp_month as i64,
     |                                
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `i32`, found `i64`

error[E0308]: mismatched types
    --> src\services\stripe.rs:2532:31
     |
2532 |                     exp_year: card_details.exp_year 
as i64,
     |                               
^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `i32`, found `i64`

error[E0599]: no function or associated item named `update` 
found for struct `stripe::Card` in the current scope
    --> src\services\stripe.rs:2711:26
     |
2711 |         let card = Card::update(&self.client, 
&card_id.parse()?, params).await?;
     |                          ^^^^^^ function or 
associated item not found in `Card`

error[E0599]: no function or associated item named `update` 
found for struct `stripe::Card` in the current scope
    --> src\services\stripe.rs:2725:26
     |
2725 |         let card = Card::update(&self.client, 
&card_id.parse()?, params).await?;
     |                          ^^^^^^ function or 
associated item not found in `Card`

error[E0599]: no function or associated item named `update` 
found for struct `stripe::Card` in the current scope
    --> src\services\stripe.rs:2742:26
     |
2742 |         let card = Card::update(&self.client, 
&card_id.parse()?, params).await?;
     |                          ^^^^^^ function or 
associated item not found in `Card`

error[E0599]: no function or associated item named `update` 
found for struct `stripe::Card` in the current scope
    --> src\services\stripe.rs:2784:26
     |
2784 |         let card = Card::update(&self.client, 
&card_id.parse()?, params).await?;
     |                          ^^^^^^ function or 
associated item not found in `Card`

error[E0599]: no function or associated item named `list` 
found for struct `stripe::IssuingAuthorization` in the 
current scope
    --> src\services\stripe.rs:2804:52
     |
2804 |         let authorizations = 
IssuingAuthorization::list(&self.client, &params).await?;
     |                                                    
^^^^ function or associated item not found in 
`IssuingAuthorization`
     |
help: there is a method `fmt_list` with a similar name, but 
with different arguments
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\wyz-0.5.1\src\fmt.rs:108:2
     |
108  | /     fn fmt_list(self) -> FmtList<Self>
109  | |     where for<'a> &'a Self: IntoIterator {
     | |________________________________________^

error[E0599]: no function or associated item named `update` 
found for struct `stripe::IssuingAuthorization` in the 
current scope
    --> src\services\stripe.rs:2813:51
     |
2813 |         let authorization = 
IssuingAuthorization::update(&self.client, 
&authorization_id.parse()?, params).await?;
     |                                                   
^^^^^^ function or associated item not found in 
`IssuingAuthorization`

error[E0599]: no function or associated item named `update` 
found for struct `stripe::IssuingAuthorization` in the 
current scope
    --> src\services\stripe.rs:2828:51
     |
2828 |         let authorization = 
IssuingAuthorization::update(&self.client, 
&authorization_id.parse()?, params).await?;
     |                                                   
^^^^^^ function or associated item not found in 
`IssuingAuthorization`

error[E0599]: no function or associated item named `list` 
found for struct `stripe::IssuingTransaction` in the 
current scope
    --> src\services\stripe.rs:2852:48
     |
2852 |         let transactions = 
IssuingTransaction::list(&self.client, &params).await?;
     |                                                ^^^^ 
function or associated item not found in 
`IssuingTransaction`
     |
help: there is a method `fmt_list` with a similar name, but 
with different arguments
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\wyz-0.5.1\src\fmt.rs:108:2
     |
108  | /     fn fmt_list(self) -> FmtList<Self>
109  | |     where for<'a> &'a Self: IntoIterator {
     | |________________________________________^

error[E0599]: no function or associated item named `create` 
found for struct `stripe::IssuingDispute` in the current 
scope
    --> src\services\stripe.rs:3216:39
     |
3216 |         let dispute = 
IssuingDispute::create(&self.client, params).await?;
     |                                       ^^^^^^ 
function or associated item not found in `IssuingDispute`

error[E0599]: no variant or associated item named 
`Submitted` found for enum `IssuingDisputeStatus` in the 
current scope
    --> src\services\stripe.rs:3237:62
     |
3237 |                 "submitted" => 
stripe::IssuingDisputeStatus::Submitted,
     |                                                      
        ^^^^^^^^^ variant or associated item not found in 
`IssuingDisputeStatus`
     |
help: there is a variant with a similar name
     |
3237 -                 "submitted" => 
stripe::IssuingDisputeStatus::Submitted,
3237 +                 "submitted" => 
stripe::IssuingDisputeStatus::Unsubmitted,
     |

error[E0599]: no function or associated item named `list` 
found for struct `stripe::IssuingDispute` in the current 
scope
    --> src\services\stripe.rs:3247:40
     |
3247 |         let disputes = 
IssuingDispute::list(&self.client, &params).await?;
     |                                        ^^^^ function 
or associated item not found in `IssuingDispute`
     |
help: there is a method `fmt_list` with a similar name, but 
with different arguments
    --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-19
49cf8c6b5b557f\wyz-0.5.1\src\fmt.rs:108:2
     |
108  | /     fn fmt_list(self) -> FmtList<Self>
109  | |     where for<'a> &'a Self: IntoIterator {
     | |________________________________________^

error[E0599]: no function or associated item named `update` 
found for struct `stripe::Dispute` in the current scope
    --> src\services\stripe.rs:3296:32
     |
3296 |         let dispute = Dispute::update(&self.client, 
&dispute_id.parse()?, params).await?;
     |                                ^^^^^^ function or 
associated item not found in `Dispute`

warning: unused variable: `card`
    --> src\services\stripe.rs:3367:13
     |
3367 |         let card = 
self.get_card_details(card_id).await?;
     |             ^^^^ help: if this is intentional, 
prefix it with an underscore: `_card`

error[E0599]: no function or associated item named `create` 
found for struct `stripe::File` in the current scope
    --> src\services\stripe.rs:3545:26
     |
3545 |         let file = File::create(&self.client, 
params).await?;
     |                          ^^^^^^ function or 
associated item not found in `File`

error[E0609]: no field `status` on type `&mut _`
   --> src\services\withdrawal.rs:237:26
    |
237 |                 response.status = 
"failed".to_string();
    |                          ^^^^^^ unknown field

warning: unused variable: `webhook_event`
   --> src\services\withdrawal.rs:408:13
    |
408 |         let webhook_event = WebhookEvent {
    |             ^^^^^^^^^^^^^ help: if this is 
intentional, prefix it with an underscore: `_webhook_event`

Some errors have detailed explanations: E0061, E0063, 
E0252, E0271, E0277, E0282, E0308, E0405, E0412...
For more information about an error, try `rustc --explain 
E0061`.
warning: `sangapay` (bin "sangapay") generated 109 warnings
error: could not compile `sangapay` (bin "sangapay") due to 
347 previous errors; 109 warnings emitted
