use std::collections::HashMap;
use std::env;
use std::sync::Arc;

use anyhow::{Context, Result, anyhow};
use backoff::{ExponentialBackoff, future::retry};
use chrono::Utc;
use log::{error, info, warn};
use mongodb::{Database, bson::{doc, oid::ObjectId}};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::models::{account::Account, transaction::SimpleTransaction as Transaction};
use crate::services::account_number::AccountNumberService;

#[derive(Debug, Serialize, Deserialize)]
pub struct ExchangeRateResponse {
    pub success: bool,
    pub timestamp: u64,
    pub base: String,
    pub date: String,
    pub rates: HashMap<String, f64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CurrencyConversionResponse {
    pub success: bool,
    pub timestamp: u64,
    pub base: String,
    pub date: String,
    pub important_rates: HashMap<String, f64>,
    pub all_rates: Option<HashMap<String, f64>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SwapBalanceRequest {
    pub source_currency: String,
    pub target_currency: String,
    pub amount: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SwapBalanceResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<SwapBalanceData>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SwapBalanceData {
    pub source_balance: f64,
    pub target_balance: f64,
    pub transaction_id: String,
    pub exchange_rate: f64,
    pub receipt: String,
}

pub struct CurrencyConversionService {
    api_key: String,
    base_url: String,
    hardcoded_symbols: Vec<String>,
    http_client: Client,
    db: Arc<Database>,
    account_service: AccountNumberService,
}

impl CurrencyConversionService {
    pub fn new(db: Arc<Database>) -> Result<Self> {
        let api_key = env::var("FIXER_API_KEY")
            .context("FIXER_API_KEY environment variable not set")?;
        
        let base_url = "https://data.fixer.io/api".to_string();
        let hardcoded_symbols = vec![
            "USD".to_string(),
            "EUR".to_string(),
            "GBP".to_string(),
        ];

        let account_service = AccountNumberService::new(db.clone());

        Ok(Self {
            api_key,
            base_url,
            hardcoded_symbols,
            http_client: Client::new(),
            db,
            account_service,
        })
    }

    pub async fn get_latest_rates_from_usd(
        &self,
        symbols: &str,
        include_all_rates: bool,
    ) -> Result<CurrencyConversionResponse> {
        let url = format!("{}/latest", self.base_url);
        
        let get_rates = || async {
            let mut params = vec![
                ("access_key", self.api_key.as_str()),
                ("base", "USD"),
            ];

            if !symbols.is_empty() {
                params.push(("symbols", symbols));
            }

            let response = self.http_client
                .get(&url)
                .query(&params)
                .send()
                .await?;

            if response.status().is_success() {
                let exchange_response: ExchangeRateResponse = response.json().await?;
                
                if !exchange_response.success {
                    return Err(reqwest::Error::from(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        "Failed to fetch latest rates"
                    )));
                }

                let important_rates: HashMap<String, f64> = self.hardcoded_symbols
                    .iter()
                    .filter_map(|symbol| {
                        exchange_response.rates.get(symbol).map(|rate| (symbol.clone(), *rate))
                    })
                    .collect();

                let all_rates = if include_all_rates {
                    Some(exchange_response.rates)
                } else {
                    None
                };

                Ok(CurrencyConversionResponse {
                    success: exchange_response.success,
                    timestamp: exchange_response.timestamp,
                    base: exchange_response.base,
                    date: exchange_response.date,
                    important_rates,
                    all_rates,
                })
            } else {
                let error_text = response.text().await?;
                Err(reqwest::Error::from(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Exchange rate API error: {}", error_text)
                )))
            }
        };

        retry(ExponentialBackoff::default(), get_rates).await
    }

    pub async fn get_exchange_rate(&self, base: &str, target: &str) -> Result<f64> {
        let symbols = self.hardcoded_symbols.join(",");
        let rates_response = self.get_latest_rates_from_usd(&symbols, false).await?;

        if !rates_response.success {
            return Err(anyhow!("Failed to fetch exchange rates"));
        }

        let rates = &rates_response.important_rates;

        if base == "USD" {
            rates.get(target)
                .copied()
                .ok_or_else(|| anyhow!("Target currency {} not found", target))
        } else if target == "USD" {
            rates.get(base)
                .map(|rate| 1.0 / rate)
                .ok_or_else(|| anyhow!("Base currency {} not found", base))
        } else {
            let base_rate = rates.get(base)
                .ok_or_else(|| anyhow!("Base currency {} not found", base))?;
            let target_rate = rates.get(target)
                .ok_or_else(|| anyhow!("Target currency {} not found", target))?;
            
            Ok(target_rate / base_rate)
        }
    }

    pub async fn swap_balances(
        &self,
        account: &mut Account,
        user_id: ObjectId,
        source_currency: &str,
        target_currency: &str,
        amount: f64,
    ) -> Result<SwapBalanceResponse> {
        if amount <= 0.0 {
            return Ok(SwapBalanceResponse {
                success: false,
                message: "Amount must be greater than zero".to_string(),
                data: None,
            });
        }

        let exchange_rate = self.get_exchange_rate(source_currency, target_currency).await?;
        let converted_amount = amount * exchange_rate;

        let source_balance = account.balance.iter_mut()
            .find(|b| b.currency == source_currency)
            .ok_or_else(|| anyhow!("Source currency {} not found in account", source_currency))?;

        let target_balance = account.balance.iter_mut()
            .find(|b| b.currency == target_currency)
            .ok_or_else(|| anyhow!("Target currency {} not found in account", target_currency))?;

        if source_balance.walletBalance < amount {
            return Ok(SwapBalanceResponse {
                success: false,
                message: "Insufficient balance in source currency".to_string(),
                data: None,
            });
        }

        source_balance.walletBalance -= amount;
        target_balance.walletBalance += converted_amount;

        let transaction_id = self.account_service.generate_transaction_id(
            &format!("swap_{}_{}_{}_{}", user_id, source_currency, target_currency, Utc::now().timestamp())
        );

        let timestamp = Utc::now().format("%Y%m%d%H%M%S").to_string();
        let receipt = self.account_service.create_transaction_receipt(
            "SP",
            "CURRENCY_SWAP",
            &timestamp,
            &transaction_id,
        );

        let transaction = Transaction {
            id: None,
            user_id,
            account_id: account.id.unwrap_or_else(|| ObjectId::new()),
            transaction_id: transaction_id.clone(),
            transaction_type: "currency_swap".to_string(),
            amount,
            currency: source_currency.to_string(),
            status: "completed".to_string(),
            description: format!("Currency swap from {} to {}", source_currency, target_currency),
            metadata: Some(serde_json::json!({
                "source_currency": source_currency,
                "target_currency": target_currency,
                "exchange_rate": exchange_rate,
                "converted_amount": converted_amount,
                "receipt": receipt
            })),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let save_transaction = || async {
            self.db
                .collection::<Transaction>("transactions")
                .insert_one(&transaction, None)
                .await
        };

        retry(ExponentialBackoff::default(), save_transaction).await?;

        let update_account = || async {
            self.db
                .collection::<Account>("accounts")
                .replace_one(
                    doc! { "_id": account.id.unwrap() },
                    account,
                    None,
                )
                .await
        };

        retry(ExponentialBackoff::default(), update_account).await?;

        info!("Currency swap completed: {} {} to {} {}", amount, source_currency, converted_amount, target_currency);

        Ok(SwapBalanceResponse {
            success: true,
            message: "Currency swap completed successfully".to_string(),
            data: Some(SwapBalanceData {
                source_balance: source_balance.walletBalance,
                target_balance: target_balance.walletBalance,
                transaction_id,
                exchange_rate,
                receipt,
            }),
        })
    }

    pub async fn convert_currency(&self, amount: f64, from: &str, to: &str) -> Result<f64> {
        let exchange_rate = self.get_exchange_rate(from, to).await?;
        Ok(amount * exchange_rate)
    }

    pub async fn get_supported_currencies(&self) -> Vec<String> {
        self.hardcoded_symbols.clone()
    }

    pub async fn validate_currency(&self, currency: &str) -> bool {
        self.hardcoded_symbols.contains(&currency.to_string())
    }

    pub async fn get_currency_info(&self, currency: &str) -> Result<Value> {
        if !self.validate_currency(currency).await {
            return Err(anyhow!("Unsupported currency: {}", currency));
        }

        let rates_response = self.get_latest_rates_from_usd(&self.hardcoded_symbols.join(","), false).await?;
        
        let rate = rates_response.important_rates.get(currency)
            .ok_or_else(|| anyhow!("Currency {} not found in rates", currency))?;

        Ok(serde_json::json!({
            "currency": currency,
            "rate_to_usd": 1.0 / rate,
            "rate_from_usd": rate,
            "last_updated": rates_response.date,
            "timestamp": rates_response.timestamp
        }))
    }

    pub async fn calculate_conversion_fee(&self, amount: f64, _from: &str, _to: &str) -> f64 {
        // Simple fee calculation: 0.5% of the amount
        amount * 0.005
    }

    pub async fn get_historical_rates(&self, date: &str, symbols: &str) -> Result<ExchangeRateResponse> {
        let url = format!("{}/{}", self.base_url, date);
        
        let get_historical = || async {
            let params = vec![
                ("access_key", self.api_key.as_str()),
                ("base", "USD"),
                ("symbols", symbols),
            ];

            let response = self.http_client
                .get(&url)
                .query(&params)
                .send()
                .await?;

            if response.status().is_success() {
                let exchange_response: ExchangeRateResponse = response.json().await?;
                
                if !exchange_response.success {
                    return Err(reqwest::Error::from(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        "Failed to fetch historical rates"
                    )));
                }

                Ok(exchange_response)
            } else {
                let error_text = response.text().await?;
                Err(reqwest::Error::from(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Historical rates API error: {}", error_text)
                )))
            }
        };

        retry(ExponentialBackoff::default(), get_historical).await
    }
}
